"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

export default function ColorDemoPage() {
  return (
    <div className="min-h-screen bg-background p-8">
      <div className="max-w-6xl mx-auto space-y-8">
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold text-foreground">企业级配色方案演示</h1>
          <p className="text-lg text-muted-foreground">专业、正式、低饱和度的企业级设计</p>
        </div>

        {/* Color Palette */}
        <Card className="bg-card border-border">
          <CardHeader>
            <CardTitle className="text-foreground">核心色彩体系</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="space-y-2">
                <div className="w-full h-16 bg-primary rounded-md"></div>
                <p className="text-sm font-medium text-foreground">深海军蓝</p>
                <p className="text-xs text-muted-foreground">Primary</p>
              </div>
              <div className="space-y-2">
                <div className="w-full h-16 bg-secondary rounded-md"></div>
                <p className="text-sm font-medium text-foreground">商务蓝</p>
                <p className="text-xs text-muted-foreground">Secondary</p>
              </div>
              <div className="space-y-2">
                <div className="w-full h-16 bg-muted rounded-md"></div>
                <p className="text-sm font-medium text-foreground">中性灰</p>
                <p className="text-xs text-muted-foreground">Muted</p>
              </div>
              <div className="space-y-2">
                <div className="w-full h-16 bg-accent rounded-md"></div>
                <p className="text-sm font-medium text-foreground">浅灰背景</p>
                <p className="text-xs text-muted-foreground">Accent</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Buttons */}
        <Card className="bg-card border-border">
          <CardHeader>
            <CardTitle className="text-foreground">按钮样式</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-wrap gap-4">
              <Button className="bg-primary hover:bg-primary/90 text-primary-foreground">
                主要按钮
              </Button>
              <Button variant="secondary" className="bg-secondary hover:bg-secondary/90 text-secondary-foreground">
                次要按钮
              </Button>
              <Button variant="outline" className="border-border text-foreground hover:bg-muted">
                边框按钮
              </Button>
              <Button variant="ghost" className="text-muted-foreground hover:bg-muted hover:text-foreground">
                幽灵按钮
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Data Cards */}
        <Card className="bg-card border-border">
          <CardHeader>
            <CardTitle className="text-foreground">数据卡片</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card className="bg-card border-border">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-xs font-medium text-muted-foreground">总用户数</p>
                      <p className="text-2xl font-bold text-foreground">12,345</p>
                      <p className="text-xs text-muted-foreground">活跃用户</p>
                    </div>
                    <div className="w-12 h-12 bg-primary rounded-md flex items-center justify-center">
                      <div className="w-6 h-6 bg-white rounded opacity-80"></div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-card border-border">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-xs font-medium text-muted-foreground">月收入</p>
                      <p className="text-2xl font-bold text-foreground">¥89,432</p>
                      <p className="text-xs text-muted-foreground">本月统计</p>
                    </div>
                    <div className="w-12 h-12 bg-secondary rounded-md flex items-center justify-center">
                      <div className="w-6 h-6 bg-white rounded opacity-80"></div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-card border-border">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-xs font-medium text-muted-foreground">完成率</p>
                      <p className="text-2xl font-bold text-foreground">94.2%</p>
                      <p className="text-xs text-muted-foreground">任务完成</p>
                    </div>
                    <div className="w-12 h-12 bg-muted-foreground rounded-md flex items-center justify-center">
                      <div className="w-6 h-6 bg-white rounded opacity-80"></div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </CardContent>
        </Card>

        {/* Typography */}
        <Card className="bg-card border-border">
          <CardHeader>
            <CardTitle className="text-foreground">文字层级</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <h1 className="text-3xl font-bold text-foreground">一级标题 - 深炭灰</h1>
              <h2 className="text-2xl font-semibold text-foreground">二级标题 - 深炭灰</h2>
              <h3 className="text-xl font-medium text-foreground">三级标题 - 深炭灰</h3>
              <p className="text-base text-foreground">正文内容 - 标准文字颜色</p>
              <p className="text-sm text-muted-foreground">辅助文字 - 中等灰色</p>
              <p className="text-xs text-muted-foreground">说明文字 - 浅灰色</p>
            </div>
          </CardContent>
        </Card>

        {/* Status Badges */}
        <Card className="bg-card border-border">
          <CardHeader>
            <CardTitle className="text-foreground">状态标识</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              <Badge className="bg-primary text-primary-foreground">重要</Badge>
              <Badge variant="secondary" className="bg-secondary text-secondary-foreground">次要</Badge>
              <Badge variant="outline" className="border-border text-foreground">边框</Badge>
              <Badge className="bg-green-600 text-white">成功</Badge>
              <Badge className="bg-orange-600 text-white">警告</Badge>
              <Badge variant="destructive">错误</Badge>
            </div>
          </CardContent>
        </Card>

        <div className="text-center space-y-2">
          <p className="text-sm text-muted-foreground">
            企业级配色方案特点：低饱和度、专业感、正式感
          </p>
          <p className="text-xs text-muted-foreground">
            圆角减少至 4px，移除装饰性元素，增强商务感
          </p>
        </div>
      </div>
    </div>
  )
}
