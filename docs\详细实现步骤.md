## 详细实现步骤

### 第一阶段：后端基础架构搭建

#### 步骤1：创建后端项目基础结构
1. 在 `backend` 目录下创建以下基础文件和目录：
   - 创建 `backend/app` 目录作为应用主目录
   - 创建 `backend/app/main.py` 作为应用入口文件
   - 创建 `backend/app/shared` 目录作为共享层
   - 创建 `backend/app/modules` 目录作为业务模块目录
   - 创建 `backend/.env.example` 环境变量示例文件
   - 创建 `backend/pyproject.toml` 项目依赖配置文件

#### 步骤2：配置项目依赖
1. 编辑 `backend/pyproject.toml`，添加以下依赖：
   - FastAPI 及其相关依赖
   - Tortoise ORM
   - PostgreSQL 连接器（asyncpg）
   - Redis 客户端（redis）
   - JWT 处理库（PyJWT）
   - 密码哈希库（bcrypt）
   - 限流相关库
   - 其他必要依赖

#### 步骤3：创建环境变量配置
1. 编辑 `backend/.env.example`，添加文档中指定的环境变量：
   - PostgreSQL 连接配置（PG_HOST, PG_PORT, PG_USER, PG_PASSWORD, PG_DB）
   - Redis 连接配置（REDIS_URL, REDIS_PASSWORD）
   - 限流配置（RATE_LIMIT_BACKEND, RATE_LIMIT_PREFIX）
   - OTP 配置（OTP_ENABLED, OTP_TTL_SECONDS）
   - 令牌黑名单配置（TOKEN_BLOCKLIST_PREFIX）
   - JWT 配置（JWT_SECRET, ACCESS_EXPIRE_SECONDS, REFRESH_EXPIRE_SECONDS）
   - CORS 配置（CORS_ORIGINS）
   - Cookie 配置（COOKIE_SECURE, COOKIE_SAMESITE）
   - 环境标识（HERMES_ENV）

#### 步骤4：实现共享层基础组件
1. 创建 `backend/app/shared/config.py`：
   - 实现环境变量加载功能
   - 提供全局配置访问接口

2. 创建 `backend/app/shared/db.py`：
   - 实现 Tortoise ORM 初始化函数
   - 提供数据库连接管理
   - 实现模型注册机制

3. 创建 `backend/app/shared/redis.py`：
   - 实现 Redis 连接管理
   - 提供键空间命名规范
   - 实现常用 Redis 操作封装
   - 实现连接生命周期管理

4. 创建 `backend/app/shared/security.py`：
   - 实现 JWT 签发和验证功能
   - 实现密码哈希和验证功能
   - 实现刷新令牌撤销检查功能
   - 提供安全相关的工具函数

5. 创建 `backend/app/shared/rate_limit.py`：
   - 实现基于 Redis 的通用限流组件
   - 支持基于 IP 和用户的限流策略
   - 实现滑动窗口和固定窗口限流算法
   - 提供限流键的 TTL 管理

6. 创建 `backend/app/shared/response.py`：
   - 实现统一响应包装
   - 实现异常处理机制
   - 提供标准化的错误响应格式

7. 创建 `backend/app/shared/utils.py`：
   - 实现通用工具函数
   - 提供常用的辅助方法

### 第二阶段：认证模块实现

#### 步骤5：创建认证模块结构
1. 在 `backend/app/modules` 下创建 `auth` 目录
2. 在 `auth` 目录下创建：
   - `models.py` - 数据模型定义
   - `schemas.py` - 请求和响应模型定义
   - `routes.py` - 路由定义
   - `services` 目录 - 业务逻辑实现
   - 在 `services` 目录下创建 `auth.py` 和 `sms.py`

#### 步骤6：实现认证数据模型
1. 编辑 `backend/app/modules/auth/models.py`：
   - 定义 User 模型，包含字段：id, email, phone, password_hash, role, is_active, created_at, updated_at
   - 设置合适的字段约束和索引

#### 步骤7：实现认证请求和响应模型
1. 编辑 `backend/app/modules/auth/schemas.py`：
   - 定义用户注册请求模型（email, password）
   - 定义用户登录请求模型（email, password）
   - 定义用户响应模型（id, email, role）
   - 定义令牌响应模型
   - 定义错误响应模型

#### 步骤8：实现认证业务逻辑
1. 编辑 `backend/app/modules/auth/services/auth.py`：
   - 实现用户注册逻辑（邮箱唯一性检查、密码哈希、用户创建）
   - 实现用户登录逻辑（密码验证、JWT 令牌生成）
   - 实现获取当前用户信息逻辑
   - 实现令牌刷新逻辑（包含刷新令牌撤销机制）
   - 实现用户登出逻辑（将刷新令牌加入黑名单）

2. 编辑 `backend/app/modules/auth/services/sms.py`：
   - 预留 OTP 相关接口（返回 501 状态码）
   - 实现基于 Redis 的验证码存储和验证接口框架
   - 实现验证码发送频率限制接口框架

#### 步骤9：实现认证路由
1. 编辑 `backend/app/modules/auth/routes.py`：
   - 实现 POST /auth/register 路由（添加 IP 限流）
   - 实现 POST /auth/login 路由（添加用户和 IP 限流）
   - 实现 GET /auth/me 路由
   - 实现 POST /auth/refresh 路由（添加用户限流）
   - 实现 POST /auth/logout 路由
   - 预留 POST /auth/otp/request 和 POST /auth/otp/verify 路由
   - 添加基于 Redis 的限流装饰器

### 第三阶段：用户模块实现

#### 步骤10：创建用户模块结构
1. 在 `backend/app/modules` 下创建 `user` 目录
2. 在 `user` 目录下创建：
   - `models.py` - 数据模型定义
   - `schemas.py` - 请求和响应模型定义
   - `routes.py` - 路由定义
   - `services.py` - 业务逻辑实现

#### 步骤11：实现用户数据模型
1. 编辑 `backend/app/modules/user/models.py`：
   - 根据需要定义用户相关的扩展模型
   - 如果有额外的用户信息，可以在这里定义

#### 步骤12：实现用户请求和响应模型
1. 编辑 `backend/app/modules/user/schemas.py`：
   - 定义用户信息请求和响应模型
   - 定义用户更新请求模型

#### 步骤13：实现用户业务逻辑
1. 编辑 `backend/app/modules/user/services.py`：
   - 实现用户信息获取逻辑
   - 实现用户信息更新逻辑
   - 实现其他用户相关业务逻辑

#### 步骤14：实现用户路由
1. 编辑 `backend/app/modules/user/routes.py`：
   - 实现用户相关的路由
   - 添加适当的权限检查

### 第四阶段：积分模块实现

#### 步骤15：创建积分模块结构
1. 在 `backend/app/modules` 下创建 `points` 目录
2. 在 `points` 目录下创建：
   - `models.py` - 数据模型定义
   - `schemas.py` - 请求和响应模型定义
   - `routes.py` - 路由定义
   - `services.py` - 业务逻辑实现

#### 步骤16：实现积分数据模型
1. 编辑 `backend/app/modules/points/models.py`：
   - 定义 PointsAccount 模型，包含字段：user_id, balance
   - 定义 PointsTransaction 模型，包含字段：id, user_id, delta, reason, meta, balance_after, created_at
   - 设置合适的字段约束和索引

#### 步骤17：实现积分请求和响应模型
1. 编辑 `backend/app/modules/points/schemas.py`：
   - 定义积分账户请求和响应模型
   - 定义积分交易请求和响应模型
   - 定义积分历史记录响应模型

#### 步骤18：实现积分业务逻辑
1. 编辑 `backend/app/modules/points/services.py`：
   - 实现获取用户积分账户逻辑
   - 实现积分增加逻辑（在同一事务中完成积分增加和流水记录）
   - 实现积分扣减逻辑（在同一事务中完成积分扣减和流水记录）
   - 实现获取积分交易历史逻辑

#### 步骤19：实现积分路由
1. 编辑 `backend/app/modules/points/routes.py`：
   - 实现获取积分账户路由
   - 实现积分交易路由
   - 实现获取积分交易历史路由
   - 添加适当的权限检查

### 第五阶段：应用入口和中间件配置

#### 步骤20：实现应用入口
1. 编辑 `backend/app/main.py`：
   - 创建 FastAPI 应用实例
   - 配置 CORS 中间件（允许 http://localhost:3000）
   - 配置 Cookie 策略（httpOnly, SameSite Lax, 开发期 Secure false）
   - 注册异常处理
   - 注册模块路由（/auth, /users, /points）
   - 实现启动时的 Tortoise 初始化和模型加载
   - 实现 Redis 连接初始化和关闭生命周期管理
   - 添加健康检查端点（包含数据库和 Redis 连接状态）

#### 步骤21：创建数据库初始化脚本
1. 创建 `backend/scripts/init_db.py`：
   - 实现数据库表创建功能
   - 可选：实现初始数据填充功能

### 第六阶段：前端对接实现

#### 步骤22：创建前端 API 封装
1. 创建 `frontend/lib/api.ts`：
   - 实现基础 fetch 封装，默认 credentials include
   - 实现统一请求头设置
   - 实现错误包装，支持 401 自动引导登录

#### 步骤23：创建前端认证方法
1. 创建 `frontend/lib/auth.ts`：
   - 实现登录方法（使用邮箱与密码）
   - 实现注册方法
   - 实现获取当前用户信息方法
   - 实现刷新会话方法
   - 实现退出登录方法

#### 步骤24：修改前端登录页
1. 编辑 `frontend/app/page.tsx`：
   - 将模拟登录逻辑替换为使用 `lib/auth.ts` 中的登录方法
   - 添加注册功能
   - 实现错误消息展示
   - 移除本地模拟登录逻辑

#### 步骤25：修改前端仪表盘页
1. 编辑 `frontend/app/dashboard/page.tsx`：
   - 在初次加载时调用 `/auth/me` 获取用户信息
   - 实现未登录时自动跳转到登录页
   - 替换 localStorage 中的登录状态检查

#### 步骤26：实现前端路由守卫
1. 创建 `frontend/components/auth-guard.tsx`：
   - 实现路由守卫组件
   - 检查用户登录状态
   - 未登录时重定向到登录页

2. 修改 `frontend/app/layout.tsx`：
   - 集成路由守卫
   - 配置需要认证的路由

### 第七阶段：测试和文档

#### 步骤27：编写测试用例
1. 为后端各模块编写单元测试
2. 为关键 API 接口编写集成测试
3. 为前端认证流程编写端到端测试

#### 步骤28：更新项目文档
1. 编辑 `README.md`：
   - 添加项目介绍
   - 添加环境配置说明
   - 添加启动步骤说明
   - 添加 API 接口文档链接

#### 步骤29：实现开发环境启动脚本
1. 创建 `backend/docker-compose.yml`：
   - 定义 PostgreSQL 服务（包含数据持久化）
   - 定义 Redis 服务（包含密码保护和数据持久化）
   - 定义应用服务
   - 配置服务间网络和依赖关系
   - 添加健康检查

2. 创建 `scripts/dev.sh`：
   - 实现一键启动开发环境
   - 包含数据库初始化步骤
   - 包含 Redis 连接测试

### 第八阶段：优化和扩展

#### 步骤30：实现日志系统
1. 在 `backend/app/shared` 下创建 `logging.py`：
   - 实现统一的日志配置
   - 支持不同环境的日志级别
   - 实现结构化日志输出
   - 集成 Redis 操作日志记录

#### 步骤31：实现监控和健康检查
1. 在 `backend/app/main.py` 中添加健康检查端点
2. 实现基本的系统监控指标
3. 添加 Redis 和 PostgreSQL 连接状态监控
4. 实现关键指标（如限流命中率、缓存命中率）的暴露

#### 步骤32：实现 OTP 功能（可选）
1. 完善 `backend/app/modules/auth/services/sms.py`：
   - 实现基于 Redis 的验证码存储和 TTL 管理
   - 实现验证码验证逻辑
   - 添加基于 Redis 的频率限制和失败计数
   - 实现验证码发送的幂等性处理

2. 更新相关路由和前端对接
3. 添加 OTP 功能的开关控制

#### 步骤33：实现缓存策略（可选）
1. 在 `backend/app/shared` 下创建 `cache.py`：
   - 实现基于 Redis 的缓存管理
   - 提供缓存键的命名规范和 TTL 管理
   - 实现缓存穿透和雪崩防护
   - 实现缓存与数据库的一致性策略

2. 为频繁访问的数据添加缓存层：
   - 用户信息缓存
   - 积分账户缓存
   - 权限信息缓存

#### 步骤34：实现分布式锁（可选）
1. 在 `backend/app/shared` 下创建 `lock.py`：
   - 实现基于 Redis 的分布式锁
   - 提供锁的超时和续期机制
   - 实现锁的可重入性

2. 在需要保证原子性的操作中使用分布式锁：
   - 积分扣减操作
   - 防重复提交
   - 定时任务执行

以上步骤涵盖了从零开始实现登录注册与积分系统的完整流程。每个步骤都基于 `docs/auth-spec.md` 文档中的设计要求，并遵循了模块化和分层架构的原则。您可以根据实际需求和资源情况，适当调整步骤的优先级和实现顺序。