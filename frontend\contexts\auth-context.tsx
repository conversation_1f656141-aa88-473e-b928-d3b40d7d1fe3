'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'
import type { User, LoginCredentials, RegisterCredentials, AuthContextType } from '@/lib/types/auth'
import { authService } from '@/lib/api/auth'
import { getErrorMessage } from '@/lib/utils/error-messages'

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    checkAuth()
  }, [])

  const checkAuth = async () => {
    try {
      const response = await authService.getCurrentUser()
      if (response.code === 0) {
        setUser(response.data)
      }
    } catch (error) {
      console.log('User not authenticated:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const login = async (credentials: LoginCredentials) => {
    setIsLoading(true)
    try {
      const response = await authService.login(credentials)
      if (response.code === 0) {
        setUser(response.data)
      } else {
        throw new Error(getErrorMessage(response.msg) || '登录失败')
      }
    } finally {
      setIsLoading(false)
    }
  }

  const register = async (credentials: RegisterCredentials) => {
    setIsLoading(true)
    try {
      const response = await authService.register(credentials)
      if (response.code === 0) {
        setUser(response.data)
      } else {
        throw new Error(getErrorMessage(response.msg) || '注册失败')
      }
    } finally {
      setIsLoading(false)
    }
  }

  const logout = async () => {
    try {
      await authService.logout()
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      setUser(null)
    }
  }

  const refreshUser = async () => {
    await checkAuth()
  }

  const value: AuthContextType = {
    user,
    isAuthenticated: !!user,
    isLoading,
    login,
    register,
    logout,
    refreshUser,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}