from tortoise import BaseDBAsync<PERSON>lient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "timestampmodel" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON COLUMN "timestampmodel"."created_at" IS '创建时间';
COMMENT ON COLUMN "timestampmodel"."updated_at" IS '更新时间';
COMMENT ON TABLE "timestampmodel" IS '时间戳混入类，为模型添加created_at和updated_at字段';
CREATE TABLE IF NOT EXISTS "users" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "email" VARCHAR(255) UNIQUE,
    "phone" VARCHAR(20) UNIQUE,
    "password_hash" VARCHAR(255) NOT NULL,
    "role" VARCHAR(5) NOT NULL DEFAULT 'user',
    "is_active" BOOL NOT NULL DEFAULT True,
    "last_login_at" TIMESTAMPTZ,
    "last_login_ip" VARCHAR(255)
);
COMMENT ON COLUMN "users"."id" IS '用户ID';
COMMENT ON COLUMN "users"."created_at" IS '创建时间';
COMMENT ON COLUMN "users"."updated_at" IS '更新时间';
COMMENT ON COLUMN "users"."email" IS '邮箱';
COMMENT ON COLUMN "users"."phone" IS '手机号';
COMMENT ON COLUMN "users"."password_hash" IS '密码hash';
COMMENT ON COLUMN "users"."role" IS '用户角色';
COMMENT ON COLUMN "users"."is_active" IS '是否启用';
COMMENT ON COLUMN "users"."last_login_at" IS '最后登录时间';
COMMENT ON COLUMN "users"."last_login_ip" IS '最后登录IP';
COMMENT ON TABLE "users" IS '用户表';
CREATE TABLE IF NOT EXISTS "aerich" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "version" VARCHAR(255) NOT NULL,
    "app" VARCHAR(100) NOT NULL,
    "content" JSONB NOT NULL
);"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        """
