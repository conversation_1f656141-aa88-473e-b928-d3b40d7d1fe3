import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  const accessToken = request.cookies.get('cookie_access')?.value
  const refreshToken = request.cookies.get('cookie_refresh')?.value
  const isAuthPage = request.nextUrl.pathname === '/'
  
  // Get the pathname of the request
  const { pathname } = request.nextUrl

  // Define public paths that don't require authentication
  const publicPaths = ['/']
  
  // Check if the current path is public
  const isPublicPath = publicPaths.includes(pathname)
  
  // 优化认证检查逻辑
  const hasValidAuth = !!accessToken || !!refreshToken
  
  // 如果用户没有任何认证凭据且尝试访问受保护的路由
  if (!hasValidAuth && !isPublicPath) {
    return NextResponse.redirect(new URL('/', request.url))
  }
  
  // 如果用户有认证凭据且尝试访问登录页面
  if (hasValidAuth && isAuthPage) {
    // 避免在登录页面上无限重定向循环
    // 只有当确实有有效的访问令牌时才重定向到仪表板
    if (accessToken) {
      return NextResponse.redirect(new URL('/dashboard', request.url))
    }
    // 如果只有刷新令牌，让用户停留在登录页，让前端处理刷新逻辑
  }
  
  return NextResponse.next()
}

export const config = {
  matcher: [
    // 排除静态资源与图片目录，避免被中间件误拦截
    '/((?!api|_next/static|_next/image|favicon.ico|icons).*)',
  ],
}