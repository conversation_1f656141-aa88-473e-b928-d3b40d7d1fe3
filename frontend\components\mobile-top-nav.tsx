"use client"

import type React from "react"
import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { LogOut, User, Menu, X, Home, Bot, ChevronDown } from "lucide-react"

// 公共样式常量
const COMMON_STYLES = {
  menuButton: "w-full flex items-center gap-3 px-4 py-3 text-sm font-medium transition-all duration-200",
  menuButtonActive: "bg-blue-600 text-white",
  menuButtonInactive: "text-gray-600 hover:bg-blue-50 hover:text-blue-600",
  subMenuButton: "w-full flex items-center gap-3 px-6 py-3 text-sm font-medium transition-all duration-200",
  activeIndicator: "ml-auto w-2 h-2 bg-white rounded-full"
}

// 状态指示器颜色映射
const getStatusColor = (status: string) => {
  switch (status) {
    case "在线": return "bg-green-500"
    case "忙碌": return "bg-yellow-500"
    default: return "bg-gray-400"
  }
}

interface Agent {
  id: string
  name: string
  url: string
  type: string
  status: string
  lastActive: string
  tasksCompleted: number
  successRate: number
}

interface MobileTopNavProps {
  selectedView: string
  onViewSelect: (view: string) => void
  onLogout: () => void
  username: string
  agents: Agent[]
}

export default function MobileTopNav({
  selectedView,
  onViewSelect,
  onLogout,
  username,
  agents,
}: MobileTopNavProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [expandedMenus, setExpandedMenus] = useState<string[]>(["ai-agents"])

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen)
  }

  const toggleSubmenu = (menuId: string) => {
    setExpandedMenus((prev) => 
      prev.includes(menuId) 
        ? prev.filter((id) => id !== menuId) 
        : [...prev, menuId]
    )
  }

  const handleMenuItemClick = (view: string) => {
    onViewSelect(view)
    setIsMenuOpen(false) // 点击菜单项后自动收起菜单
  }



  return (
    <>
      {/* 顶部导航栏 - 仅在移动端显示 */}
      <header className="md:hidden fixed top-0 left-0 right-0 z-50 bg-white border-b border-gray-200 shadow-sm">
        <div className="flex items-center justify-between px-4 py-3">
          {/* 左侧：汉堡菜单按钮 */}
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleMenu}
            className="text-gray-700 hover:bg-blue-50 hover:text-blue-600 rounded-lg transition-all duration-200 hover:scale-105 active:scale-95 min-h-[40px] min-w-[40px]"
          >
            <div className={`transition-transform duration-200 ${isMenuOpen ? "rotate-90" : ""}`}>
              {isMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </div>
          </Button>

          {/* 中间：标题 */}
          <h1 className="text-lg font-semibold text-gray-900">智能客服引擎系统 · 赫尔墨</h1>

          {/* 右侧：用户头像 */}
          <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
            <User className="h-4 w-4 text-white" />
          </div>
        </div>
      </header>

      {/* 透明高斯模糊遮罩 */}
      {isMenuOpen && (
        <div
          className="md:hidden fixed inset-0 z-40 bg-white/20 backdrop-blur-sm"
          onClick={() => setIsMenuOpen(false)}
        />
      )}

      {/* 下拉菜单 */}
      <div
        className={`
          md:hidden fixed top-14 left-0 right-0 z-50
          bg-white border-b border-gray-200 shadow-lg
          transform transition-all duration-300 ease-out
          ${
            isMenuOpen
              ? "translate-y-0 opacity-100 visible"
              : "-translate-y-full opacity-0 invisible"
          }
        `}
      >
        <div className="max-h-[calc(100vh-56px)] overflow-y-auto">
          {/* 用户信息 */}
          <div className="px-4 py-3 bg-blue-50 border-b border-blue-100">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                <User className="h-5 w-5 text-white" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {username || "手系 Agent"}
                </p>
                <p className="text-xs text-gray-500">管理员</p>
              </div>
            </div>
          </div>

          {/* 菜单项 */}
          <nav className="py-2">
            {/* 首页 */}
            <button
              onClick={() => handleMenuItemClick("home")}
              className={`${
                COMMON_STYLES.menuButton
              } ${
                selectedView === "home"
                  ? COMMON_STYLES.menuButtonActive
                  : COMMON_STYLES.menuButtonInactive
              }`}
            >
              <Home className={`h-5 w-5 flex-shrink-0 ${
                selectedView === "home" ? "text-white" : "text-gray-500"
              }`} />
              <span>首页</span>
              {selectedView === "home" && (
                <div className={COMMON_STYLES.activeIndicator} />
              )}
            </button>

            {/* AI 专家分组 */}
            <div className="border-t border-gray-100 mt-2 pt-2">
              <button
                onClick={() => toggleSubmenu("ai-agents")}
                className="w-full flex items-center justify-between gap-3 px-4 py-3 text-sm font-medium text-gray-500 hover:bg-blue-50 hover:text-blue-600 transition-all duration-200"
              >
                <div className="flex items-center gap-2">
                  <Bot className="h-5 w-5" />
                  AI 专家
                </div>
                <ChevronDown
                  className={`h-4 w-4 transition-transform duration-200 ${
                    expandedMenus.includes("ai-agents") ? "rotate-180" : ""
                  }`}
                />
              </button>

              {/* AI 专家列表 */}
              <div
                className={`overflow-hidden transition-all duration-300 ${
                  expandedMenus.includes("ai-agents") 
                    ? "max-h-96 opacity-100" 
                    : "max-h-0 opacity-0"
                }`}
              >
                <div className="space-y-1 mt-1">
                  {agents.map((agent) => (
                    <button
                      key={agent.id}
                      onClick={() => handleMenuItemClick(agent.id)}
                      className={`${
                        COMMON_STYLES.subMenuButton
                      } ${
                        selectedView === agent.id
                          ? COMMON_STYLES.menuButtonActive
                          : COMMON_STYLES.menuButtonInactive
                      }`}
                    >
                      <div
                        className={`w-2 h-2 rounded-full flex-shrink-0 ${getStatusColor(agent.status)}`}
                      />
                      <span className="truncate">{agent.name}</span>
                      {selectedView === agent.id && (
                        <div className={COMMON_STYLES.activeIndicator} />
                      )}
                    </button>
                  ))}
                </div>
              </div>
            </div>

            {/* 退出登录 */}
            <div className="border-t border-gray-100 mt-2 pt-2">
              <button
                onClick={() => {
                  onLogout()
                  setIsMenuOpen(false)
                }}
                className={`${COMMON_STYLES.menuButton} ${COMMON_STYLES.menuButtonInactive}`}
              >
                <LogOut className="h-5 w-5" />
                <span>退出登录</span>
              </button>
            </div>
          </nav>
        </div>
      </div>
    </>
  )
}