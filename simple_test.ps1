Write-Host "Testing health endpoint..."
$healthResponse = Invoke-WebRequest -Uri "http://localhost:8000/health" -Method GET
Write-Host "Health Status: $($healthResponse.StatusCode)"
Write-Host "Health Body: $($healthResponse.Content)"

Write-Host "`nTesting register..."
$body = '{"phone":"13800138001","password":"12345678"}'
try {
    $registerResponse = Invoke-WebRequest -Uri "http://localhost:8000/auth/register" -Method POST -ContentType "application/json" -Body $body -SessionVariable session
    Write-Host "Register Status: $($registerResponse.StatusCode)"
    Write-Host "Register Body: $($registerResponse.Content)"
    
    # 显示cookies
    $cookies = $session.Cookies.GetCookies("http://localhost:8000")
    Write-Host "Cookies count: $($cookies.Count)"
    foreach ($cookie in $cookies) {
        Write-Host "Cookie: $($cookie.Name) = $($cookie.Value)"
    }
    
    Write-Host "`nTesting /auth/me with session..."
    $meResponse = Invoke-WebRequest -Uri "http://localhost:8000/auth/me" -Method GET -WebSession $session
    Write-Host "Me Status: $($meResponse.StatusCode)"
    Write-Host "Me Body: $($meResponse.Content)"
    
} catch {
    Write-Host "Error: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        $stream = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($stream)
        $responseBody = $reader.ReadToEnd()
        Write-Host "Error Body: $responseBody"
    }
}
