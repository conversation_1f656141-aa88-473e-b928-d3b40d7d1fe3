Write-Host "Testing authentication flow..."

$loginBody = '{"phone":"13800138001","password":"12345678"}'

Write-Host "Step 1: Login..."
$loginResponse = Invoke-WebRequest -Uri "http://localhost:8000/auth/login" -Method POST -ContentType "application/json" -Body $loginBody -SessionVariable session
Write-Host "Login Status: $($loginResponse.StatusCode)"

$cookies = $session.Cookies.GetCookies("http://localhost:8000")
Write-Host "Cookies count: $($cookies.Count)"

Write-Host "Step 2: Test /auth/me..."
try {
    $meResponse = Invoke-WebRequest -Uri "http://localhost:8000/auth/me" -Method GET -WebSession $session
    Write-Host "Me Status: $($meResponse.StatusCode)"
    Write-Host "Me Body: $($meResponse.Content)"
} catch {
    Write-Host "Me Error: $($_.Exception.Message)"
    Write-Host "Response Status: $($_.Exception.Response.StatusCode)"
}
