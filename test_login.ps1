# 测试注册和登录接口
$registerHash = @{
    phone = "13800138000"
    password = "123456"
}
$registerData = $registerHash | ConvertTo-Json

$loginHash = @{
    phone = "13800138000"
    password = "123456"
}
$loginData = $loginHash | ConvertTo-Json

Write-Host "Testing register..."
try {
    $registerResponse = Invoke-WebRequest -Uri "http://localhost:8000/auth/register" -Method POST -ContentType "application/json" -Body $registerData -SessionVariable session
    Write-Host "Register Response Status: $($registerResponse.StatusCode)"
    Write-Host "Register Response Body: $($registerResponse.Content)"
    Write-Host "Register Cookies: $($session.Cookies.GetCookies('http://localhost:8000'))"
} catch {
    Write-Host "Register Error: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Register Error Response Body: $responseBody"
    }
}

Write-Host "`nTesting login..."
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8000/auth/login" -Method POST -ContentType "application/json" -Body $loginData -SessionVariable loginSession
    Write-Host "Login Response Status: $($response.StatusCode)"
    Write-Host "Login Response Body: $($response.Content)"
    Write-Host "Login Cookies: $($loginSession.Cookies.GetCookies('http://localhost:8000'))"

    # 测试获取用户信息
    Write-Host "`nTesting /auth/me..."
    $meResponse = Invoke-WebRequest -Uri "http://localhost:8000/auth/me" -Method GET -WebSession $loginSession
    Write-Host "Me Response Status: $($meResponse.StatusCode)"
    Write-Host "Me Response Body: $($meResponse.Content)"
} catch {
    Write-Host "Login Error: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Login Error Response Body: $responseBody"
    }
}
