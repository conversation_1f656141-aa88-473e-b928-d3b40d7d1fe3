"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { LogOut, User, Menu, X, ChevronLeft, ChevronRight, Home, Bot, ChevronDown } from "lucide-react"

// 公共样式常量
const COMMON_STYLES = {
  button: "w-full flex items-center gap-3 px-3 py-3 text-sm font-medium rounded-lg transition-all duration-200",
  buttonHover: "hover:bg-blue-50 hover:text-blue-600",
  buttonActive: "bg-blue-600 text-white shadow-sm",
  buttonInactive: "text-gray-600",
  tooltip: "absolute left-full top-0 ml-2 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 ease-out z-50 group-hover:pointer-events-auto",
  tooltipContent: "bg-white border border-gray-200 rounded-lg shadow-xl px-3 py-2 whitespace-nowrap",
  tooltipArrow: "before:content-[''] before:absolute before:left-[-6px] before:top-3 before:w-0 before:h-0 before:border-t-[6px] before:border-b-[6px] before:border-r-[6px] before:border-transparent before:border-r-white",
  statusDot: "w-2 h-2 rounded-full flex-shrink-0"
}

// 辅助函数
const getButtonClassName = (isActive: boolean, extraClasses = "") => {
  const baseClasses = `${COMMON_STYLES.button} ${extraClasses}`
  return isActive 
    ? `${baseClasses} ${COMMON_STYLES.buttonActive}`
    : `${baseClasses} ${COMMON_STYLES.buttonInactive} ${COMMON_STYLES.buttonHover}`
}

const getStatusDotClassName = (status: string) => {
  const statusColors = {
    "在线": "bg-green-500",
    "忙碌": "bg-yellow-500",
    "离线": "bg-gray-400"
  }
  return `${COMMON_STYLES.statusDot} ${statusColors[status as keyof typeof statusColors] || statusColors["离线"]}`
}

// 可复用的Tooltip组件
const SidebarTooltip = ({ children, text, show }: { children: React.ReactNode; text: string; show: boolean }) => {
  return (
    <div className="group relative">
      {children}
      {show && (
        <div className={COMMON_STYLES.tooltip}>
          <div className={`${COMMON_STYLES.tooltipContent} ${COMMON_STYLES.tooltipArrow}`}>
            <p className="text-sm font-medium text-gray-700">{text}</p>
          </div>
        </div>
      )}
    </div>
  )
}

interface Agent {
  id: string
  name: string
  url: string
  type: string
  status: string
  lastActive: string
  tasksCompleted: number
  successRate: number
}

interface SidebarProps {
  selectedView: string
  onViewSelect: (view: string) => void
  onLogout: () => void
  username: string
  agents: Agent[]
  mobileMenuOpen: boolean
  onToggleMobileMenu: () => void
  onTouchStart: (e: React.TouchEvent) => void
  onTouchMove: (e: React.TouchEvent) => void
  onTouchEnd: () => void
}

export default function Sidebar({
  selectedView,
  onViewSelect,
  onLogout,
  username,
  agents,
  mobileMenuOpen,
  onToggleMobileMenu,
  onTouchStart,
  onTouchMove,
  onTouchEnd,
}: SidebarProps) {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false) // 默认展开
  const [expandedMenus, setExpandedMenus] = useState<string[]>([])

  // 响应式侧边栏逻辑
  useEffect(() => {
    const handleResize = () => {
      const isLargeScreen = window.innerWidth >= 1280 // xl breakpoint
      const isMediumScreen = window.innerWidth >= 1024 // lg breakpoint
      
      if (isLargeScreen) {
        // 大屏幕默认展开
        setSidebarCollapsed(false)
      } else if (isMediumScreen) {
        // 中等屏幕默认收起但可手动展开
        setSidebarCollapsed(true)
      }
    }

    // 初始化时检查屏幕大小
    handleResize()
    
    // 监听窗口大小变化
    window.addEventListener('resize', handleResize)
    
    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed)
  }

  const toggleSubmenu = (menuId: string) => {
    setExpandedMenus((prev) => (prev.includes(menuId) ? prev.filter((id) => id !== menuId) : [...prev, menuId]))
  }

  const handleBackdropClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    onToggleMobileMenu()
  }

  return (
    <>
      {/* 在移动端完全隐藏侧边栏 */}
      <aside
        className={`
          hidden md:flex
          bg-white
          border-r border-gray-200
          flex-col
          transition-all duration-300 ease-out
          ${sidebarCollapsed ? "w-16" : "w-64 xl:w-72"}
          h-screen
        `}
      >
        <div className={`border-b border-gray-200 ${sidebarCollapsed ? "p-2" : "p-4"}`}>
          <div className={`flex items-center mb-3 ${sidebarCollapsed ? "justify-center" : "justify-between"}`}>
            {!sidebarCollapsed && <h1 className="text-lg font-semibold text-gray-900">智能客服引擎系统 · 赫尔墨</h1>}
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleSidebar}
              className="text-gray-500 hover:bg-blue-50 hover:text-blue-600 rounded-lg transition-all duration-200"
            >
              {sidebarCollapsed ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
            </Button>
          </div>

          {sidebarCollapsed ? (
            <div className="flex justify-center mb-3 group relative">
              <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center cursor-pointer hover:bg-blue-700 transition-all duration-200 hover:scale-105">
                <User className="h-5 w-5 text-white" />
              </div>
              <div className="absolute left-full top-0 ml-3 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 ease-out z-50">
                <div className="bg-white border border-gray-200 rounded-lg shadow-xl px-3 py-2 whitespace-nowrap before:content-[''] before:absolute before:left-[-6px] before:top-3 before:w-0 before:h-0 before:border-t-[6px] before:border-b-[6px] before:border-r-[6px] before:border-transparent before:border-r-white">
                  <p className="text-sm font-medium text-gray-700">{username || "手系 Agent"}</p>
                  <p className="text-xs text-gray-500">管理员</p>
                </div>
              </div>
            </div>
          ) : (
            <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg mb-3 border border-blue-100">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <User className="h-4 w-4 text-white" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">{username || "手系 Agent"}</p>
                <p className="text-xs text-gray-500">管理员</p>
              </div>
            </div>
          )}
        </div>

        <nav className={`flex-1 ${sidebarCollapsed ? "p-2 overflow-visible" : "p-4 overflow-y-auto"}`}>
          <div className="space-y-1">
            {/* Dashboard Home */}
            <SidebarTooltip text="首页" show={sidebarCollapsed}>
              <button
                onClick={() => onViewSelect("home")}
                className={getButtonClassName(
                  selectedView === "home",
                  sidebarCollapsed ? "justify-center" : ""
                )}
              >
                <Home className={`h-5 w-5 flex-shrink-0 ${selectedView === "home" ? "text-white" : "text-gray-500"}`} />
                {!sidebarCollapsed && <span>首页</span>}
                {!sidebarCollapsed && selectedView === "home" && (
                  <div className="ml-auto w-2 h-2 bg-white rounded-full" />
                )}
              </button>
            </SidebarTooltip>

            {/* AI Agents Section */}
            <div className="group relative">
              {sidebarCollapsed ? (
                <>
                  <div
                    className={getButtonClassName(
                      agents.some((agent) => selectedView === agent.id),
                      "justify-center cursor-default"
                    )}
                  >
                    <Bot
                      className={`h-5 w-5 flex-shrink-0 ${agents.some((agent) => selectedView === agent.id) ? "text-white" : "text-gray-500"}`}
                    />
                  </div>

                  <div className="absolute left-full top-0 ml-2 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 ease-out z-50 group-hover:pointer-events-auto">
                    <div className="bg-white border border-gray-200 rounded-lg shadow-xl w-[220px] py-2 relative">
                      {/* Invisible bridge area to prevent tooltip disappearing */}
                      <div className="absolute right-full top-0 w-2 h-full"></div>
                      <div className="absolute left-[-6px] top-4 w-0 h-0 border-t-[6px] border-b-[6px] border-r-[6px] border-transparent border-r-white"></div>
                      <div className="py-1 max-h-[300px] overflow-y-auto overflow-x-hidden">
                        <div className="space-y-0.5">
                          {agents.map((agent) => (
                            <button
                              key={agent.id}
                              onClick={() => onViewSelect(agent.id)}
                              className={
                                selectedView === agent.id
                                  ? `${COMMON_STYLES.button} rounded-md mx-1 py-2 ${COMMON_STYLES.buttonActive} hover:bg-blue-700 min-w-0`
                                  : `${COMMON_STYLES.button} rounded-md mx-1 py-2 text-gray-700 hover:bg-blue-50 hover:text-blue-600 min-w-0`
                              }
                            >
                              <div className={getStatusDotClassName(agent.status)}></div>
                              <span className="truncate flex-1 text-left min-w-0">{agent.name}</span>
                              {selectedView === agent.id && (
                                <div className="w-2 h-2 bg-white rounded-full flex-shrink-0" />
                              )}
                            </button>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </>
              ) : (
                <>
                    <button
                      onClick={() => toggleSubmenu("ai-agents")}
                      className={`${COMMON_STYLES.button} justify-between ${COMMON_STYLES.buttonInactive} ${COMMON_STYLES.buttonHover}`}
                    >
                      <div className="flex items-center gap-2">
                        <Bot className="h-5 w-5" />
                        AI 专家
                      </div>
                      <ChevronDown
                        className={`h-3 w-3 transition-transform duration-200 ${
                          expandedMenus.includes("ai-agents") ? "rotate-180" : ""
                        }`}
                      />
                  </button>

                  <div
                    className={`overflow-hidden transition-all duration-300 ${
                      expandedMenus.includes("ai-agents") ? "max-h-96 opacity-100" : "max-h-0 opacity-0"
                    }`}
                  >
                    <div className="space-y-0.5 mt-1">
                      {agents.map((agent) => (
                        <button
                          key={agent.id}
                          onClick={() => onViewSelect(agent.id)}
                          className={getButtonClassName(
                            selectedView === agent.id,
                            "min-h-[40px] px-4 py-2.5 ml-2"
                          )}
                        >
                          <div className={getStatusDotClassName(agent.status)}></div>
                          <span className="truncate">{agent.name}</span>
                          {selectedView === agent.id && <div className="ml-auto w-2 h-2 bg-white rounded-full" />}
                        </button>
                      ))}
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>
        </nav>

        <div className={`border-t border-gray-200 ${sidebarCollapsed ? "p-2" : "p-4"}`}>
          <SidebarTooltip text="退出登录" show={sidebarCollapsed}>
            <Button
              variant="ghost"
              onClick={onLogout}
              className={`
                ${COMMON_STYLES.button} ${COMMON_STYLES.buttonInactive} ${COMMON_STYLES.buttonHover}
                ${sidebarCollapsed ? "justify-center" : "justify-start"}
              `}
            >
              <LogOut className="h-5 w-5" />
              {!sidebarCollapsed && <span className="ml-3 text-sm">退出登录</span>}
            </Button>
          </SidebarTooltip>
        </div>
      </aside>
    </>
  )
}
