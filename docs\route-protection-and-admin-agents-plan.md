# 后端路由保护与管理员/智能体访问方案

本方案落地“管理员可进行用户管理 + 所有登录用户可使用智能体”，在不破坏现有登录与响应风格的前提下，使用子路由级统一保护与最小权限控制，确保清晰、可扩展、易维护。

## 1. 背景与目标

- 目标 A：管理员可在后端进行用户管理（查看、启用/禁用等）。
- 目标 B：只要登录即可使用智能体能力，暂不做细粒度授权与配额。
- 现状复用：
  - JWT 校验：[verify_token](backend/app/shared/security.py:56)
  - 当前用户：[get_current_user](backend/app/modules/auth/services/auth.py:103)
  - 统一响应：[error_response](backend/app/shared/errors.py:22)、[success_response](backend/app/shared/errors.py:40)

## 2. 架构决策

- 路由保护范围：采用“子路由级保护”。在模块级 APIRouter 上挂登录依赖，模块内端点默认受保护。
- 权限粒度：
  - 智能体模块：所有“已登录”用户可用（不做细粒度授权）。
  - 用户管理模块：管理员专用，叠加角色依赖校验 admin。
- 错误风格：保持当前每个路由内部 try/except 并返回 [error_response](backend/app/shared/errors.py:22) 的最小改动方案；可选在后续增加全局异常处理器。
- 可扩展性：预留角色扩展、智能体配额、API Key 双通道等依赖作为之后的增量。

## 3. 目录与新增文件

- 新增依赖封装：
  - [backend/app/shared/dependencies.py](backend/app/shared/dependencies.py:1)
    - [def require_user()](backend/app/shared/dependencies.py:1)
    - [def require_roles\*](backend/app/shared/dependencies.py:20)
    - [def require_admin()](backend/app/shared/dependencies.py:40)
- 用户管理路由（管理员）：
  - [backend/app/modules/users/router.py](backend/app/modules/users/router.py:1)
- 智能体路由（所有登录用户）：
  - [backend/app/modules/agents/router.py](backend/app/modules/agents/router.py:1)
- 主应用注册：
  - 在 [backend/app/main.py](backend/app/main.py:41) 调用 app.include_router 注册上述模块

说明：本方案文档先定义接口与依赖规范，代码实现由后续任务按本规范落地。

## 4. 认证依赖设计

- [def require_user(request) -> dict](backend/app/shared/dependencies.py:1)
  - 从 Cookie 读取 access token，调用 [get_current_user](backend/app/modules/auth/services/auth.py:103)。
  - 成功返回“当前用户 dict”，失败抛出错误（如 [ErrorCode.UNAUTHORIZED](backend/app/shared/errors.py:5)、[ErrorCode.INVALID_TOKEN](backend/app/shared/errors.py:5)）。
- [def require_roles(*roles)](backend/app/shared/dependencies.py:20)
  - 闭包依赖，校验当前用户的 role 是否在允许列表中。
  - 校验失败建议返回 403，此时可选引入 [ErrorCode.FORBIDDEN](backend/app/shared/errors.py:1)（映射 403），或先用 UNAUTHORIZED。
- [def require_admin()](backend/app/shared/dependencies.py:40)
  - 语义别名：等价 [require_roles](backend/app/shared/dependencies.py:20) with [UserRole.ADMIN](backend/app/modules/auth/models.py:5)。

依赖使用方式示例（伪代码，实际在路由中通过 Depends 注入）：
- @router.get("/list", dependencies=[Depends(require_admin)])
- async def list_users(current_user=Depends(require_user)): ...

## 5. 用户管理模块（仅管理员）

- 路由文件：[backend/app/modules/users/router.py](backend/app/modules/users/router.py:1)
- APIRouter 保护：
  - router = APIRouter(prefix="/users", tags=["users"], dependencies=[Depends(require_user)])
  - 管理员端点再叠加 Depends([require_admin](backend/app/shared/dependencies.py:40))
- 建议最小接口集：
  - GET /users
    - 权限：管理员
    - 功能：分页/列表查询用户
    - 依赖：Depends([require_admin](backend/app/shared/dependencies.py:40))
  - GET /users/{id}
    - 权限：管理员
    - 功能：查看用户详情
    - 依赖：Depends([require_admin](backend/app/shared/dependencies.py:40))
  - PATCH /users/{id}/status
    - 权限：管理员
    - 功能：启用/禁用用户 is_active
    - 依赖：Depends([require_admin](backend/app/shared/dependencies.py:40))
- 数据模型复用：
  - [UserRole](backend/app/modules/auth/models.py:5)、[User](backend/app/modules/auth/models.py:10)
- 响应约定：
  - 与现有 [auth.router.me](backend/app/modules/auth/router.py:87) 一致，将 role 转换为字符串返回，确保前端可读。

## 6. 智能体模块（所有登录用户）

- 路由文件：[backend/app/modules/agents/router.py](backend/app/modules/agents/router.py:1)
- APIRouter 保护：
  - router = APIRouter(prefix="/agents", tags=["agents"], dependencies=[Depends(require_user)])
- 建议最小接口：
  - POST /agents/chat
    - 权限：所有已登录用户
    - 功能：发起与智能体的对话
    - 依赖：模块级 require_user 已覆盖；如需当前用户，在处理函数参数注入 current_user=Depends([require_user](backend/app/shared/dependencies.py:1))
  - 可选：POST /agents/{agent_id}/chat（为未来细粒度授权/配额预留）
- 后端联动（可选）：
  - 服务层代理：[backend/app/modules/agents/services/agents.py](backend/app/modules/agents/services/agents.py:1)
  - 新增配置 [settings.agents_service_url](backend/app/shared/config.py:1) 指向“智能体后端”地址
  - 路由内调用服务层，将上下文中的用户信息用于审计

## 7. 错误码与响应约定

- 沿用现有：
  - [ErrorCode](backend/app/shared/errors.py:5)
  - [error_response](backend/app/shared/errors.py:22)
  - [success_response](backend/app/shared/errors.py:40)
- 建议新增（可选）：
  - ErrorCode.FORBIDDEN（403）用于“已登录但权限不足”
- 统一约束：
  - 未登录：返回 401 + [ErrorCode.UNAUTHORIZED](backend/app/shared/errors.py:5)
  - 登录但权限不足（管理员端点）：403 + FORBIDDEN（若暂不引入则用 401）
  - 令牌无效/过期：401 + INVALID_TOKEN
  - 服务器错误：500 + INTERNAL_ERROR

## 8. 主应用注册

在 [main.app](backend/app/main.py:19) 中注册新路由（参考）：
- [from backend.app.modules.users import router as users_router](backend/app/modules/users/router.py:1)
- [from backend.app.modules.agents import router as agents_router](backend/app/modules/agents/router.py:1)
- [app.include_router(users_router, prefix="/users", tags=["users"])](backend/app/main.py:41)
- [app.include_router(agents_router, prefix="/agents", tags=["agents"])](backend/app/main.py:41)

## 9. 前端约定

- 登录后，前端可根据 /auth/me 返回的 role 决定是否展示“用户管理”入口；最终鉴权以后端为准。
- 调用智能体接口时，确保携带相同站点 Cookie（httpOnly cookie_access/cookie_refresh），跨域已由 [CORSMiddleware](backend/app/main.py:26) 配置允许 credentials。

## 10. 运维与管理员初始化

- 初始化至少一个管理员账号：
  - 方式 A：一次性脚本将某用户的 role 设为 [UserRole.ADMIN](backend/app/modules/auth/models.py:5)
  - 方式 B：临时管理端点（受 IP 限制和 require_admin 双重保护），完成后移除
- 审计建议：记录管理员操作日志（谁在何时对哪个用户进行了什么操作）

## 11. 验收标准

- 未登录访问 /users 或 /agents 任一端点：返回 401 + UNAUTHORIZED，结构为 [error_response](backend/app/shared/errors.py:22)。
- 登录普通用户：
  - 访问 /users 列表：返回 403 + FORBIDDEN（或 401，如果未引入 FORBIDDEN）
  - 访问 /agents/chat：成功，返回 [success_response](backend/app/shared/errors.py:40)
- 登录管理员：
  - 可访问 /users 列表与修改端点；状态更新在 DB 生效
- 响应结构与代码枚举始终一致，便于前端本地化提示

## 12. 流程图

用户管理端点
```mermaid
flowchart LR
client --> U[APIRouter /users]
U --> A[require_user]
A --> B[get_current_user]
U --> C[require_admin]
C --> D[role_check]
D --> H[handler]
H --> R[success_response]
```

智能体端点
```mermaid
flowchart LR
client --> AG[APIRouter /agents]
AG --> AU[require_user]
AU --> CU[get_current_user]
AG --> AH[handler]
AH --> SVC[agents service proxy]
SVC --> RR[success_response]
```

## 13. 实施步骤（落地顺序）

1) 新增 [dependencies.py](backend/app/shared/dependencies.py:1)：实现 [require_user](backend/app/shared/dependencies.py:1)、[require_roles](backend/app/shared/dependencies.py:20)、[require_admin](backend/app/shared/dependencies.py:40)
2) 新增 [users/router.py](backend/app/modules/users/router.py:1)：APIRouter 统一挂 [require_user](backend/app/shared/dependencies.py:1)，管理员端点叠加 [require_admin](backend/app/shared/dependencies.py:40)
3) 新增 [agents/router.py](backend/app/modules/agents/router.py:1)：APIRouter 统一挂 [require_user](backend/app/shared/dependencies.py:1)，实现 POST /agents/chat
4) 在 [main.py](backend/app/main.py:41) 注册 users 与 agents 路由
5) 可选：在 [errors.py](backend/app/shared/errors.py:1) 新增 FORBIDDEN=403；可选在 main 注册全局异常处理器
6) 可选：新增 [agents/services/agents.py](backend/app/modules/agents/services/agents.py:1) 与 [settings.agents_service_url](backend/app/shared/config.py:1)
7) 运维初始化管理员账号
8) 验收联调

## 14. 附：示例接口骨架（伪代码）

users/router.py（管理员）
- [router = APIRouter(prefix="/users", tags=["users"], dependencies=[Depends(require_user)])](backend/app/modules/users/router.py:1)
- [@router.get("")](backend/app/modules/users/router.py:10) + Depends([require_admin](backend/app/shared/dependencies.py:40))
- [@router.patch("/{id}/status")](backend/app/modules/users/router.py:20) + Depends([require_admin](backend/app/shared/dependencies.py:40))

agents/router.py（所有登录用户）
- [router = APIRouter(prefix="/agents", tags=["agents"], dependencies=[Depends(require_user)])](backend/app/modules/agents/router.py:1)
- [@router.post("/chat")](backend/app/modules/agents/router.py:20) 处理智能体会话

注：以上为规范与骨架，具体字段与分页参数根据实际需求收敛。
