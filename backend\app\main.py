from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from backend.app.shared.config import settings
from backend.app.shared.db import init_db, close_db

# 使用lifespan注册初始化和关闭数据库连接
@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动阶段： 一次性初始化数据库连接池
    await init_db()
    try:
        yield
    finally:
        # 结束阶段： 关闭数据库连接池
        await close_db()
        
app = FastAPI(
    title="Hermes API",
    description="Hermes 项目后端 API",
    version="0.1.0",
    lifespan=lifespan,
)

# 配置 CORS 跨域
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 监控检查端点
@app.get("/health", tags=["system"])
async def health():
    return {"ok": True, "service": "hermes-api", "message": "服务在运行"}


from backend.app.modules.auth.router import router as auth_router
app.include_router(auth_router, prefix="/auth", tags=["auth"])

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "backend.app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=(settings.env == "dev"),
    )