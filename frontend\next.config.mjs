/** @type {import('next').NextConfig} */
const nextConfig = {
  // 移除 output: 'export' 以支持 middleware
  trailingSlash: true,
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  // 添加图片优化配置
  images: {
    unoptimized: false,
    formats: ['image/avif', 'image/webp'],
  },
  // 添加允许的开发源配置
  allowedDevOrigins: ['***************'],
}

export default nextConfig
