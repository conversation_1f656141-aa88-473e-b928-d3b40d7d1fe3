# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is the "Hermes" (赫尔墨斯) intelligent customer service engine system - a modern full-stack application with a React/Next.js frontend and FastAPI backend. The system is designed to provide AI-powered customer service capabilities with user authentication, agent management, and analytics dashboard.

## Environment Requirements

- Python 3.10+
- Node.js 18+
- PostgreSQL 12+
- Redis (optional, for caching and rate limiting)

## Development Commands

### Frontend (Next.js)
```bash
# Navigate to frontend directory
cd frontend

# Install dependencies
npm install

# Development server
npm run dev

# Build for production
npm run build

# Start production server
npm run start

# Lint code
npm run lint
```

### Backend (FastAPI)
```bash
# Navigate to backend directory
cd backend

# Install dependencies
pip install -r requirements.txt

# Set up environment variables
cp .env.example .env
# Edit .env file with your database credentials and other settings

# Development server with auto-reload
uvicorn app.main:app --reload
# OR
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# Production server
uvicorn app.main:app
# OR
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000
```

### Database Migrations
```bash
# First-time setup
python -m aerich init-db

# Generate new migration
python -m aerich migrate --name "migration_name"

# Apply migrations
python -m aerich upgrade

# Downgrade migrations
python -m aerich downgrade
```

## Health Check and API Documentation

- Health check: `curl http://localhost:8000/health`
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## Architecture

### Backend Structure
The backend follows a modular architecture with clear separation of concerns:

**Core Layers:**
- `backend/app/main.py` - FastAPI application entry point
- `backend/app/shared/` - Shared utilities and configurations
  - `config.py` - Environment configuration and database settings
  - `db.py` - Database connection and Tortoise ORM setup
  - `security.py` - JWT handling and password hashing
  - `rate_limit.py` - Rate limiting middleware
  - `response.py` - Standardized response format
  - `errors.py` - Error handling utilities

**Module Structure:**
- `backend/app/modules/` - Business modules (auth, user, points)
  - Each module contains: `models.py`, `schemas.py`, `router.py`, `services/`
  - Modules depend only on the shared layer, not on each other
  - Clean separation: routes → services → shared → models

**Key Dependencies:**
- FastAPI with async support
- Tortoise ORM with PostgreSQL
- Pydantic for data validation
- JWT for authentication
- Redis for caching and rate limiting

### Frontend Structure
The frontend is a Next.js application with modern React patterns:

**Core Components:**
- `frontend/app/` - Next.js app router structure
  - `page.tsx` - Login page with authentication
  - `dashboard/page.tsx` - Main dashboard with agent management
  - `layout.tsx` - Root layout with fonts and styling

**UI Components:**
- `frontend/components/ui/` - Radix UI components with Tailwind styling
- `frontend/components/` - Custom components (sidebar, mobile navigation)
- `frontend/components/auth/` - Authentication-related components
- `frontend/lib/` - Utility functions and helpers
- `frontend/contexts/` - React context providers
- `frontend/hooks/` - Custom React hooks

**Key Dependencies:**
- Next.js with App Router
- React
- TypeScript
- Tailwind CSS for styling
- Radix UI for accessible components
- Chakra UI integration
- Framer Motion for animations
- Recharts for data visualization

## Development Patterns

### Backend Conventions
1. **Module Structure**: Each business feature is a separate module
2. **Dependency Direction**: Routes → Services → Shared → Models
3. **Database**: Use Tortoise ORM with PostgreSQL, migrations via Aerich
4. **Authentication**: JWT tokens with httpOnly cookies
5. **Rate Limiting**: Redis-based rate limiting per endpoint
6. **Response Format**: Consistent JSON response wrapper with code/msg/data

### Frontend Conventions
1. **Authentication**: Uses JWT authentication with httpOnly cookies
2. **Routing**: Next.js App Router with client-side navigation
3. **Styling**: Tailwind CSS with component variants
4. **UI Components**: Radix UI components for accessibility
5. **State Management**: React hooks and context for state management
6. **Responsive Design**: Mobile-first approach with touch gestures

### Database Schema
**Users Table** (`users`):
- `id`, `user_name`, `email`, `phone`, `password_hash`
- `role` (enum: admin/user), `is_active`
- `last_login_at`, `last_login_ip`
- Timestamps from `TimestampModel`

**Migration System**:
- Uses Aerich for Tortoise ORM migrations
- Migration files stored in `migrations/models/`
- Automatic schema generation in development mode
- **Important**: All table structure changes must be done through migrations, not automatic generation

### Environment Configuration
**Backend Environment** (`.env` in `backend/` directory):
```
# Running environment
HERMES_ENV=dev

# Database configuration
PG_HOST=127.0.0.1
PG_PORT=5432
PG_USER=postgres
PG_PASSWORD=your_password
PG_DB=hermes

# JWT configuration
JWT_SECRET=change_me_in_production
ACCESS_EXPIRE_SECONDS=600
REFRESH_EXPIRE_SECONDS=1209600

# CORS configuration
CORS_ORIGINS=http://localhost:3000

# Redis configuration (optional)
REDIS_URL=redis://127.0.0.1:6379/0
REDIS_PASSWORD=

# Rate limiting configuration
RATE_LIMIT_BACKEND=redis
RATE_LIMIT_PREFIX=rl

# OTP configuration
OTP_ENABLED=false
OTP_TTL_SECONDS=300

# Cookie configuration
COOKIE_SECURE=false
COOKIE_SAMESITE=Lax

# Token blocklist configuration
TOKEN_BLOCKLIST_PREFIX=tb
```

**Frontend Environment** (`.env` in `frontend/` directory):
```
# API Base URL
NEXT_PUBLIC_API_BASE_URL=http://localhost:8000

# Authentication
NEXT_PUBLIC_AUTH_ENABLED=true

# Other environment variables
```

## Key Features

### Authentication System
- Email/password authentication with JWT
- Role-based access control (admin/user)
- Rate limiting on authentication endpoints
- Refresh token rotation
- OTP support (reserved for future)

### Agent Management
- Dashboard for managing AI agents
- Agent status monitoring (online/offline)
- Performance metrics and analytics
- Embedded agent interfaces via iframes
- Support for multiple agent types (引流, 转化, etc.)

### UI/UX Features
- Responsive design with mobile-first approach
- Touch gestures for mobile navigation
- Loading states and error handling
- Real-time dashboard updates
- Modern glassmorphism design

## Integration Points

### Frontend to Backend API
- Authentication endpoints: `/auth/*`
- User management: `/users/*`
- Points system: `/points/*`
- All responses follow `{code, msg, data}` format

### External Services
- **PostgreSQL**: Primary database
- **Redis**: Caching and rate limiting
- **External AI agents**: Via embedded iframes
- **JWT**: Token-based authentication

## Troubleshooting

### Common Issues
1. **Database connection failures**
   - Check PostgreSQL service is running
   - Verify `.env` database configuration
   - Ensure database has been created

2. **Migration failures**
   - Verify Aerich configuration in `pyproject.toml`
   - Check `backend/app/shared/config.py` for TORTOISE_ORM configuration

3. **Application startup failures**
   - Verify all dependencies are installed
   - Check environment variable configuration
   - Review logs for specific errors

## File Naming Conventions

### Backend
- Models: `models.py` (Tortoise ORM models)
- Schemas: `schemas.py` (Pydantic models)
- Routes: `router.py` (FastAPI route handlers)
- Services: `services/*.py` (Business logic)

### Frontend
- Pages: `page.tsx` (Next.js page components)
- Components: `*.tsx` (TypeScript React components)
- Utilities: `*.ts` (TypeScript utility functions)
- Styles: `*.css` (Global styles)
- Hooks: `use-*.ts` (Custom React hooks)