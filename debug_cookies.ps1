Write-Host "Testing login and cookie handling..."

# 先登录已存在的用户
$loginBody = '{"phone":"13800138001","password":"12345678"}'
try {
    Write-Host "Step 1: Login..."
    $loginResponse = Invoke-WebRequest -Uri "http://localhost:8000/auth/login" -Method POST -ContentType "application/json" -Body $loginBody -SessionVariable session
    Write-Host "Login Status: $($loginResponse.StatusCode)"
    Write-Host "Login Body: $($loginResponse.Content)"
    
    # 显示cookies详情
    $cookies = $session.Cookies.GetCookies("http://localhost:8000")
    Write-Host "Cookies count: $($cookies.Count)"
    foreach ($cookie in $cookies) {
        Write-Host "Cookie: $($cookie.Name) = $($cookie.Value)"
        Write-Host "  Domain: $($cookie.Domain)"
        Write-Host "  Path: $($cookie.Path)"
        Write-Host "  HttpOnly: $($cookie.HttpOnly)"
        Write-Host "  Secure: $($cookie.Secure)"
        Write-Host "  Expired: $($cookie.Expired)"
    }
    
    Write-Host "`nStep 2: Testing /auth/me with session..."
    $meResponse = Invoke-WebRequest -Uri "http://localhost:8000/auth/me" -Method GET -WebSession $session
    Write-Host "Me Status: $($meResponse.StatusCode)"
    Write-Host "Me Body: $($meResponse.Content)"
    
} catch {
    Write-Host "Error: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        $stream = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($stream)
        $responseBody = $reader.ReadToEnd()
        Write-Host "Error Body: $responseBody"
        Write-Host "Error Status: $($_.Exception.Response.StatusCode)"
    }
}

Write-Host "`nStep 3: Manual cookie test..."
# 手动设置cookie头测试
try {
    $accessToken = $session.Cookies.GetCookies("http://localhost:8000") | Where-Object { $_.Name -eq "cookie_access" } | Select-Object -ExpandProperty Value
    if ($accessToken) {
        Write-Host "Found access token: $($accessToken.Substring(0, 50))..."
        $headers = @{
            "Cookie" = "cookie_access=$accessToken"
        }
        $manualResponse = Invoke-WebRequest -Uri "http://localhost:8000/auth/me" -Method GET -Headers $headers
        Write-Host "Manual Status: $($manualResponse.StatusCode)"
        Write-Host "Manual Body: $($manualResponse.Content)"
    } else {
        Write-Host "No access token found in cookies"
    }
} catch {
    Write-Host "Manual Error: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        $stream = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($stream)
        $responseBody = $reader.ReadToEnd()
        Write-Host "Manual Error Body: $responseBody"
    }
}
