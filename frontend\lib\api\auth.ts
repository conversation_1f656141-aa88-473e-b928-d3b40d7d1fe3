import { apiClient } from './client'
import type { LoginCredentials, RegisterCredentials, AuthResponse, User } from '@/lib/types/auth'
import { normalizeRole } from '@/lib/utils/validation'

export const authService = {
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    const response = await apiClient.post<AuthResponse>('/auth/login', credentials)
    if (response.data) {
      response.data.role = normalizeRole(response.data.role)
    }
    return response
  },

  async register(credentials: RegisterCredentials): Promise<AuthResponse> {
    const response = await apiClient.post<AuthResponse>('/auth/register', credentials)
    if (response.data) {
      response.data.role = normalizeRole(response.data.role)
    }
    return response
  },

  async getCurrentUser(): Promise<{ code: number; msg: string; data: User }> {
    const response = await apiClient.get<{ code: number; msg: string; data: User }>('/auth/me')
    if (response.data) {
      response.data.role = normalizeRole(response.data.role)
    }
    return response
  },

  async logout(): Promise<{ code: number; msg: string; data: null }> {
    const response = await apiClient.post<{ code: number; msg: string; data: null }>('/auth/logout')
    return response
  },
}