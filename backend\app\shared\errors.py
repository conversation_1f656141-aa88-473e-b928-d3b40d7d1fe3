from enum import Enum
from typing import Dict, Any, Optional, Tuple


class ErrorCode(str, Enum):
    """统一的错误码枚举
    
    用于统一后端API响应中的错误消息，替代之前混合使用的中英文错误消息。
    前端可以基于这些枚举值做本地化和可读性映射。
    """
    OK = "ok"
    INVALID_CREDENTIALS = "invalid_credentials"  # 凭证无效（如用户名/密码错误）
    INVALID_PHONE = "invalid_phone"  # 手机号格式无效
    INVALID_PASSWORD = "invalid_password"  # 密码格式无效
    PHONE_EXISTS = "phone_exists"  # 手机号已存在
    UNAUTHORIZED = "unauthorized"  # 未授权或未登录
    INVALID_TOKEN = "invalid_token"  # 无效的令牌
    USER_NOT_FOUND = "user_not_found"  # 用户不存在
    INTERNAL_ERROR = "internal_error"  # 服务器内部错误


def error_response(code: int, msg: str, data: Any = None) -> Dict[str, Any]:
    """创建标准格式的错误响应
    
    Args:
        code: HTTP状态码或自定义错误码
        msg: 错误消息，应使用ErrorCode枚举值
        data: 可选的额外数据
        
    Returns:
        包含code、msg和data的标准格式响应字典
    """
    return {
        "code": code,
        "msg": msg,
        "data": data
    }


def success_response(data: Any = None) -> Dict[str, Any]:
    """创建标准格式的成功响应
    
    Args:
        data: 响应数据
        
    Returns:
        包含code、msg和data的标准格式响应字典
    """
    return {
        "code": 0,
        "msg": ErrorCode.OK,
        "data": data
    }


# 错误码与HTTP状态码的映射关系
ERROR_CODE_TO_HTTP_STATUS = {
    ErrorCode.INVALID_CREDENTIALS: 401,
    ErrorCode.INVALID_PHONE: 400,
    ErrorCode.INVALID_PASSWORD: 400,
    ErrorCode.PHONE_EXISTS: 409,  # Conflict
    ErrorCode.UNAUTHORIZED: 401,
    ErrorCode.INVALID_TOKEN: 401,
    ErrorCode.USER_NOT_FOUND: 404,
    ErrorCode.INTERNAL_ERROR: 500
}


def get_error_details(error_code: ErrorCode) -> Tuple[int, str]:
    """获取错误码对应的HTTP状态码和错误消息
    
    Args:
        error_code: 错误码枚举值
        
    Returns:
        (HTTP状态码, 错误消息)元组
    """
    return ERROR_CODE_TO_HTTP_STATUS.get(error_code, 500), error_code