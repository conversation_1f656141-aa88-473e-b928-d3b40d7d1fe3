from fastapi import APIRouter, HTTPException, Response, Request
from backend.app.modules.auth.schemas import RegisterIn, LoginIn
from backend.app.modules.auth.services.auth import register_with_phone, login_with_phone, get_current_user
from backend.app.shared.security import set_auth_cookies, clear_auth_cookies, verify_token, create_access_token, COOKIE_REFRESH, set_access_cookie
from backend.app.shared.errors import ErrorCode, success_response, error_response, get_error_details

router = APIRouter()

@router.post("/register")
async def register(payload: RegisterIn, response: Response):
    """
    用户注册接口
    Args:
        payload: RegisterIn
    Returns:
        dict: RegisterOut
    """
    try:
        user = await register_with_phone(payload.phone, payload.password)
        role = user["role"]
        user['role'] = role.value if hasattr(role, "value") else str(role)
        #设置cookie
        set_auth_cookies(response, user["access_token"], user["refresh_token"])
        
        return success_response({
            "id": user["id"],
            "phone": user["phone"],
            "user_name": user["user_name"],
            "role": user["role"],
            "is_active": user["is_active"],
            "tokens": {
                "access": user["access_token"],
                "refresh": user["refresh_token"]
            }
        })
    
    except ValueError as e:
        # 统一从 ValueError 中解析 ErrorCode
        err = e.args[0] if e.args else ErrorCode.INTERNAL_ERROR
        if isinstance(err, ErrorCode):
            http_status, error_msg = get_error_details(err)
            response.status_code = http_status
            return error_response(http_status, error_msg)
        elif isinstance(err, str) and err in [ec.value for ec in ErrorCode]:
            http_status, error_msg = get_error_details(ErrorCode(err))
            response.status_code = http_status
            return error_response(http_status, error_msg)
        # 兼容其他错误
        response.status_code = 500
        return error_response(500, ErrorCode.INTERNAL_ERROR)
    except Exception:
        response.status_code = 500
        return error_response(500, ErrorCode.INTERNAL_ERROR)

@router.post("/login")
async def login(payload: LoginIn, response: Response):
    """
    用户登录接口
    Args:
        payload: LoginIn
    Returns:
        dict: UserOut
    """
    try:
        user = await login_with_phone(payload.phone, payload.password)
        role = user["role"]
        user['role'] = role.value if hasattr(role, "value") else str(role)
        
        # 设置Cookie
        set_auth_cookies(response, user["access_token"], user["refresh_token"])
        
        return success_response({
            "id": user["id"],
            "phone": user["phone"],
            "user_name": user["user_name"],
            "role": user["role"],
            "is_active": user["is_active"],
            "tokens": {
                "access": user["access_token"],
                "refresh": user["refresh_token"]
            }
        })
    except ValueError as e:
        # 统一从 ValueError 中解析 ErrorCode
        err = e.args[0] if e.args else ErrorCode.INTERNAL_ERROR
        if isinstance(err, ErrorCode):
            http_status, error_msg = get_error_details(err)
            response.status_code = http_status
            return error_response(http_status, error_msg)
        elif isinstance(err, str) and err in [ec.value for ec in ErrorCode]:
            http_status, error_msg = get_error_details(ErrorCode(err))
            response.status_code = http_status
            return error_response(http_status, error_msg)
        # 兼容其他错误
        response.status_code = 500
        return error_response(500, ErrorCode.INTERNAL_ERROR)
    except Exception:
        response.status_code = 500
        return error_response(500, ErrorCode.INTERNAL_ERROR)

@router.get("/me")
async def me(request: Request, response: Response):
    """
    获取当前用户信息
    Args:
        request: Request
    Returns:
        dict: UserOut
    """
    try:
        user = await get_current_user(request)
        role = user["role"]
        user['role'] = role.value if hasattr(role, "value") else str(role)
        
        return success_response(user)
    except ValueError as e:
        # 统一从 ValueError 中解析 ErrorCode
        err = e.args[0] if e.args else ErrorCode.INTERNAL_ERROR
        if isinstance(err, ErrorCode):
            http_status, error_msg = get_error_details(err)
            response.status_code = http_status
            return error_response(http_status, error_msg)
        elif isinstance(err, str) and err in [ec.value for ec in ErrorCode]:
            http_status, error_msg = get_error_details(ErrorCode(err))
            response.status_code = http_status
            return error_response(http_status, error_msg)
        # 兼容其他错误
        response.status_code = 500
        return error_response(500, ErrorCode.INTERNAL_ERROR)
    except Exception:
        response.status_code = 500
        return error_response(500, ErrorCode.INTERNAL_ERROR)

@router.post("/refresh")
async def refresh(request: Request, response: Response):
    """
    刷新访问令牌
    使用 httpOnly cookie_refresh 刷新 access，仅更新 cookie_access
    Returns:
        dict: {code,msg}
    """
    try:
        refresh_token = request.cookies.get(COOKIE_REFRESH)
        if not refresh_token:
            return error_response(401, ErrorCode.UNAUTHORIZED)
        
        # 验证刷新令牌
        try:
            payload = verify_token(refresh_token, "refresh")
            user_id = payload.get("sub")
        except ValueError:
            return error_response(401, ErrorCode.INVALID_TOKEN)
        
        # 可选：验证用户存在且活跃
        try:
            # 延迟导入，避免循环依赖
            from backend.app.modules.auth.models import User
            user = await User.filter(id=user_id).first()
            if not user or not user.is_active:
                return error_response(401, ErrorCode.UNAUTHORIZED)
        except Exception:
            # 即便用户查询异常，也避免泄露细节
            response.status_code = 500
            return error_response(500, ErrorCode.INTERNAL_ERROR)
        
        # 仅签发新的 access 并设置 cookie_access
        new_access = create_access_token(user_id)
        set_access_cookie(response, new_access)
        return success_response(None)
    except Exception:
        response.status_code = 500
        return error_response(500, ErrorCode.INTERNAL_ERROR)

@router.post("/logout")
async def logout(response: Response):
    """
    用户登出接口
    Args:
        response: Response
    Returns:
        dict: success message
    """
    # 清除Cookie
    clear_auth_cookies(response)
    
    return success_response(None)