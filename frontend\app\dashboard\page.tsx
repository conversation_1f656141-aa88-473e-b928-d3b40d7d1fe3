"use client"

import type React from "react"
import { useEffect, useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Activity, Users, Loader2, AlertCircle, BarChart3, TrendingUp, Bot, Cpu, HardDrive, CheckCircle2, TrendingDown, RefreshCw, Settings, Bell, Clock, MessageSquare } from "lucide-react"
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line } from "recharts"
import Sidebar from "@/components/sidebar"
import MobileTopNav from "@/components/mobile-top-nav"
import { useAuth } from "@/hooks/use-auth"

// Enhanced mock data for realistic demonstration
const agents = [
  {
    id: "agent1",
    name: "C端引流",
    url: "https://aihermes.trsb555.com/chat/share?shareId=cQRTQNC001jTvX9J6DpwpclS",
    type: "分析",
    status: "在线",
    lastActive: "刚刚",
    tasksCompleted: 89,
    successRate: 96.2,
  },
  {
    id: "agent2",
    name: "C端转化",
    url: "https://aihermes.trsb555.com/chat/share?shareId=mi9IGmIfVBNBoPqHRTX7Qnw6",
    type: "创作",
    status: "在线",
    lastActive: "5分钟前",
    tasksCompleted: 156,
    successRate: 94.8,
  },
  {
    id: "agent3",
    name: "B端引流",
    url: "https://aihermes.trsb555.com/chat/share?shareId=aOX3o1JU3r3YnhSIx21DrVyk",
    type: "创作",
    status: "在线",
    lastActive: "5分钟前",
    tasksCompleted: 156,
    successRate: 94.8,
  },
  {
    id: "agent4",
    name: "B端转化",
    url: "https://aihermes.trsb555.com/chat/share?shareId=wQJQ4wBxaXCln8RE2fZ6jx4w",
    type: "创作",
    status: "在线",
    lastActive: "5分钟前",
    tasksCompleted: 156,
    successRate: 94.8,
  },
  {
    id: "agent5",
    name: "赫尔墨Plus",
    url: "https://aihermes.trsb555.com/chat/share?shareId=xTaEoMWqqfmxD4nnblqbsRUw",
    type: "创作",
    status: "在线",
    lastActive: "5分钟前",
    tasksCompleted: 156,
    successRate: 94.8,
  }
]

const dashboardStats = [
  {
    title: "专家数量",
    value: "5",
    subtitle: "在线:5",
    gradient: "from-blue-500 to-blue-600",
    icon: Bot,
    trend: "+2",
    trendDirection: "up",
  },
  {
    title: "问题解决率",
    value: "98.2%",
    subtitle: "今日平均",
    gradient: "from-emerald-500 to-emerald-600",
    icon: CheckCircle2,
    trend: "+2.1%",
    trendDirection: "up",
  },
  {
    title: "平均响应时间",
    value: "5秒",
    subtitle: "较昨日",
    gradient: "from-orange-500 to-orange-600",
    icon: Clock,
    trend: "-0.3秒",
    trendDirection: "down",
  },
  {
    title: "今日会话量",
    value: "1,247",
    subtitle: "已处理",
    gradient: "from-purple-500 to-purple-600",
    icon: MessageSquare,
    trend: "+156",
    trendDirection: "up",
  },
]

export default function DashboardPage() {
  const { user, isAuthenticated, isLoading, logout } = useAuth()
  const [selectedView, setSelectedView] = useState<string>("home")
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const [touchStartX, setTouchStartX] = useState<number | null>(null)
  const [touchCurrentX, setTouchCurrentX] = useState<number | null>(null)
  const [isPageLoading, setIsLoading] = useState(false)
  const [iframeError, setIframeError] = useState(false)
  const router = useRouter()

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push("/")
      return
    }
  }, [isAuthenticated, isLoading, router])

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
      </div>
    )
  }

  if (!isAuthenticated) {
    return null // Will redirect via useEffect
  }

  // Enhanced mobile menu accessibility and keyboard support
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape" && mobileMenuOpen) {
        setMobileMenuOpen(false)
      }
    }

    const handleResize = () => {
      // Close mobile menu when switching to desktop view
      if (window.innerWidth >= 1024 && mobileMenuOpen) {
        setMobileMenuOpen(false)
      }
    }

    document.addEventListener("keydown", handleKeyDown)
    window.addEventListener("resize", handleResize)

    return () => {
      document.removeEventListener("keydown", handleKeyDown)
      window.removeEventListener("resize", handleResize)
    }
  }, [mobileMenuOpen])

  const handleLogout = async () => {
    await logout()
    // Redirect will be handled by middleware
  }

  // Enhanced touch handling for mobile menu
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStartX(e.touches[0].clientX)
    setTouchCurrentX(e.touches[0].clientX)
  }

  const handleTouchMove = (e: React.TouchEvent) => {
    if (touchStartX === null) return
    setTouchCurrentX(e.touches[0].clientX)
  }

  const handleTouchEnd = () => {
    if (touchStartX === null || touchCurrentX === null) return

    const deltaX = touchCurrentX - touchStartX
    const threshold = 50 // Minimum swipe distance

    // Swipe right to open menu (when closed)
    if (deltaX > threshold && !mobileMenuOpen) {
      setMobileMenuOpen(true)
    }
    // Swipe left to close menu (when open)
    else if (deltaX < -threshold && mobileMenuOpen) {
      setMobileMenuOpen(false)
    }

    setTouchStartX(null)
    setTouchCurrentX(null)
  }

  // Close mobile menu when clicking outside
  const handleBackdropClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    setMobileMenuOpen(false)
  }

  const handleViewSelect = (view: string) => {
    // Renamed from handleAgentSelect to handleViewSelect
    if (view === "home") {
      setSelectedView("home")
      setMobileMenuOpen(false)
      return
    }

    setIsLoading(true)
    setIframeError(false)
    setSelectedView(view)
    setMobileMenuOpen(false)
    setTimeout(() => setIsLoading(false), 1000)
  }

  return (
    <div className="h-svh bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex flex-col md:flex-row overflow-hidden">
      {/* 移动端顶部导航栏 */}
      <MobileTopNav
        selectedView={selectedView}
        onViewSelect={handleViewSelect}
        onLogout={handleLogout}
        username={user?.user_name || '用户'}
        agents={agents}
      />
      
      {/* 桌面端侧边栏 */}
      <Sidebar
        selectedView={selectedView}
        onViewSelect={handleViewSelect}
        onLogout={handleLogout}
        username={user?.user_name || '用户'}
        agents={agents}
        mobileMenuOpen={mobileMenuOpen}
        onToggleMobileMenu={() => setMobileMenuOpen(!mobileMenuOpen)}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      />

      <main className="flex-1 p-4 lg:p-6 overflow-y-auto min-h-0 pt-[73px] md:pt-4">
        {selectedView === "home" ? (
          <div className="flex flex-col h-full min-h-0 gap-6">
            {/* 页面头部 */}
            <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-6 shadow-sm border-0">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                  <h1 className="text-2xl font-bold text-gray-900 mb-1">
                    欢迎回来，{user?.user_name || '用户'}
                  </h1>
                  <p className="text-gray-600">
                    {new Date().toLocaleDateString('zh-CN', { 
                      year: 'numeric', 
                      month: 'long', 
                      day: 'numeric',
                      weekday: 'long'
                    })}
                  </p>
                </div>
                <div className="flex items-center gap-3">
                  {[
                    { icon: RefreshCw, label: "刷新数据" },
                    { icon: Bell, label: "通知" },
                    { icon: Settings, label: "设置" }
                  ].map(({ icon: Icon, label }) => (
                    <Button 
                      key={label}
                      variant="outline" 
                      size="sm" 
                      className="bg-white/80 border-gray-200 hover:bg-white hover:shadow-md transition-all duration-200"
                    >
                      <Icon className="w-4 h-4 mr-2" />
                      {label}
                    </Button>
                  ))}
                </div>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-4">
              {dashboardStats.map((stat, index) => {
                const IconComponent = stat.icon
                return (
                  <Card key={index} className="bg-white/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 group">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex-1">
                          <p className="text-sm font-medium text-gray-600 mb-1">{stat.title}</p>
                          <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                          <p className="text-xs text-gray-500 mt-1">{stat.subtitle}</p>
                        </div>
                        <div className={`w-12 h-12 bg-gradient-to-br ${stat.gradient} rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                          <IconComponent className="w-6 h-6 text-white" />
                        </div>
                      </div>
                      {stat.trend && (
                        <div className="flex items-center justify-between pt-3 border-t border-gray-100">
                          <div className="flex items-center gap-1">
                            {stat.trendDirection === "up" ? (
                              <TrendingUp className="w-3 h-3 text-green-500" />
                            ) : (
                              <TrendingDown className="w-3 h-3 text-red-500" />
                            )}
                            <span className={`text-sm font-semibold ${
                              stat.trendDirection === "up" ? "text-green-600" : "text-red-600"
                            }`}>
                              {stat.trend}
                            </span>
                          </div>
                          <span className="text-xs text-gray-400">vs 昨天</span>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                )
              })}
            </div>

            

            {/* 移除“Agent 状态总览”区块以避免首页超出一屏 */}
          </div>
        ) : (
          (() => {
            const selectedAgent = agents.find((agent) => agent.id === selectedView)
            if (selectedAgent) {
              return (
                <Card className="h-full bg-transparent border-0 p-0 gap-0">
                  <CardContent className="h-full p-0">
                    {isLoading ? (
                      <div className="h-full flex items-center justify-center">
                        <div className="flex flex-col items-center gap-4">
                          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
                          <p className="text-blue-600">加载 {selectedAgent.name}...</p>
                        </div>
                      </div>
                    ) : iframeError ? (
                      <div className="h-full flex items-center justify-center">
                        <div className="flex flex-col items-center gap-4 text-center">
                          <AlertCircle className="h-12 w-12 text-red-500" />
                          <div>
                            <p className="text-blue-900 font-medium">加载失败</p>
                            <p className="text-blue-600 text-sm mt-1">无法加载 {selectedAgent.name}</p>
                          </div>
                          <Button onClick={() => handleViewSelect(selectedView)} variant="outline" size="sm">
                            重试
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <iframe
                        src={selectedAgent.url}
                        className="w-full h-full border-0 rounded-lg"
                        title={selectedAgent.name}
                        onError={() => setIframeError(true)}
                        sandbox="allow-scripts allow-same-origin allow-forms"
                      />
                    )}
                  </CardContent>
                </Card>
              )
            }

            return (
              <Card className="h-full bg-white border-blue-200 p-0 gap-0">
                <CardContent className="h-full p-0">
                  <div className="h-full flex items-center justify-center">
                    <div className="text-center">
                      <Activity className="h-16 w-16 text-blue-400 mx-auto mb-4" />
                      <h2 className="text-xl font-semibold text-blue-900 mb-2">功能模块</h2>
                      <p className="text-blue-600">此功能正在开发中...</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })()
        )}
      </main>
    </div>
  )
}
