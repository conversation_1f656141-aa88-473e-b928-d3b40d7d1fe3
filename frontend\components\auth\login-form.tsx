'use client'

import React, { useState } from 'react'
import Image from 'next/image'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { User, Lock, Loader2 } from 'lucide-react'
import { useAuth } from '@/hooks/use-auth'
import { validateLoginForm, sanitizeInput } from '@/lib/utils/validation'

interface LoginFormData {
  phone: string
  password: string
}

interface LoginFormProps {
  onSuccess?: () => void
  redirectTo?: string
}

export function LoginForm({ onSuccess, redirectTo = '/dashboard' }: LoginFormProps) {
  const { login, isLoading } = useAuth()
  const [error, setError] = useState<string>('')
  const [formData, setFormData] = useState<LoginFormData>({
    phone: '',
    password: ''
  })
  const [validationErrors, setValidationErrors] = useState<{
    phone?: string
    password?: string
  }>({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    const sanitizedValue = sanitizeInput(value)
    setFormData(prev => ({ ...prev, [name]: sanitizedValue }))
    
    // Clear validation error when user starts typing
    if (validationErrors[name as keyof typeof validationErrors]) {
      setValidationErrors(prev => ({ ...prev, [name]: undefined }))
    }
  }

  const validateForm = () => {
    const validation = validateLoginForm(formData)
    setValidationErrors(validation.errors)
    return validation.isValid
  }

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setError('')

    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)
    try {
      await login(formData)
      onSuccess?.()
      // Note: Redirect will be handled by middleware
    } catch (err) {
      setError(err instanceof Error ? err.message : '登录失败，请重试')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card className="w-[400px] max-w-[95vw] bg-white/60 backdrop-blur-lg border-blue-300/30 shadow-2xl rounded-2xl">
      <CardHeader className="text-center pb-6 pt-6">
        <div className="mx-auto mb-4 w-16 h-16 flex items-center justify-center relative">
          <Image 
            src="/icons/logo.svg" 
            alt="Logo" 
            width={64}
            height={64}
            priority
          />
        </div>
        <CardTitle className="text-xl font-bold text-gray-900">
          智能客服引擎系统 · 赫尔墨斯
        </CardTitle>
      </CardHeader>
      <CardContent className="px-6 pb-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="phone">手机号</Label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                <User className="h-5 w-5 text-gray-400" />
              </div>
              <Input
                id="phone"
                name="phone"
                type="tel"
                placeholder="请输入手机号"
                className="pl-12 h-12 bg-white/70 border-gray-200 rounded-xl"
                value={formData.phone}
                onChange={handleInputChange}
                disabled={isSubmitting || isLoading}
              />
            </div>
            {validationErrors.phone && (
              <p className="text-sm text-red-600">{validationErrors.phone}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="password">密码</Label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                <Lock className="h-5 w-5 text-gray-400" />
              </div>
              <Input
                id="password"
                name="password"
                type="password"
                placeholder="请输入密码"
                className="pl-12 h-12 bg-white/70 border-gray-200 rounded-xl"
                value={formData.password}
                onChange={handleInputChange}
                disabled={isSubmitting || isLoading}
              />
            </div>
            {validationErrors.password && (
              <p className="text-sm text-red-600">{validationErrors.password}</p>
            )}
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <Button
            type="submit"
            className="w-full h-12 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
            disabled={isSubmitting || isLoading}
          >
            {isSubmitting || isLoading ? (
              <div className="flex items-center space-x-2">
                <Loader2 className="w-4 h-4 animate-spin" />
                <span>登录中...</span>
              </div>
            ) : (
              '安全登录'
            )}
          </Button>
        </form>

        <div className="mt-8 pt-6 border-t border-gray-200/50">
          <div className="flex items-center justify-between text-sm text-gray-500">
            <div className="flex items-center space-x-4">
              <a href="#" className="hover:text-blue-600 transition-colors">忘记密码?</a>
            </div>
            <div className="flex items-center space-x-1">
              <svg className="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
              </svg>
              <span className="text-xs">安全加密</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}