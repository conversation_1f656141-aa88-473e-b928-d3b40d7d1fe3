from enum import Enum
from tortoise import fields
from backend.app.shared.db import TimestampModel

class UserRole(str, Enum):
    ADMIN = "admin"
    USER = "user"
    
class User(TimestampModel):
    """
    users 表 （最小集，登录审计）
    - email/phone 可选但唯一
    - hash 密码
    - 角色和状态
    - 创建和更新时间由 TimestampModel 生成 在share/db.py中
    - 登录审计 last_login_at 和 last_login_ip
    """
    id = fields.IntField(pk=True, description="用户ID")
    user_name = fields.CharField(max_length=255, null=True, unique=True, description="用户名")
    email = fields.CharField(max_length=255, null=True, unique=True, description="邮箱")
    phone = fields.CharField(max_length=20, null=True, unique=True, description="手机号")
    password_hash = fields.CharField(max_length=255, null=False, description="密码hash")
    role = fields.CharEnumField(enum_type=UserRole, default=UserRole.USER, description="用户角色")
    is_active = fields.BooleanField(default=True, description="是否启用")
    last_login_at = fields.DatetimeField(null=True, description="最后登录时间")
    last_login_ip = fields.CharField(max_length=255, null=True, description="最后登录IP")
    
    class Meta:
        table = "users"
        table_description = "用户表"
        
        def __str__(self) -> str:
            return self.user_name or self.email or f"user:{self.id}"