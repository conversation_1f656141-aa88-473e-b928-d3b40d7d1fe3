// Validation utilities using custom validation functions
// This avoids dependency on zod for now

export const validatePhone = (phone: string): string | null => {
  if (!phone) return '手机号不能为空'
  if (!/^1[3-9]\d{9}$/.test(phone)) return '请输入有效的手机号'
  return null
}

export const validatePassword = (password: string): string | null => {
  if (!password) return '密码不能为空'
  if (password.length < 8) return '密码至少8位'
  if (password.length > 64) return '密码最多64位'
  if (/\s/.test(password)) return '密码不能包含空格'
  return null
}

export const validateLoginForm = (data: { phone: string; password: string }) => {
  const errors: { phone?: string; password?: string } = {}
  
  const phoneError = validatePhone(data.phone)
  if (phoneError) errors.phone = phoneError
  
  const passwordError = validatePassword(data.password)
  if (passwordError) errors.password = passwordError
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  }
}

export const validateRegisterForm = (data: { phone: string; password: string }) => {
  return validateLoginForm(data)
}

export const normalizeRole = (role: string): 'USER' | 'ADMIN' => {
  if (role === 'admin') return 'ADMIN'
  if (role === 'user') return 'USER'
  // Default to USER for unknown roles
  return 'USER'
}

export const sanitizeInput = (input: string): string => {
  return input
    .replace(/<[^>]*>/g, '') // Remove HTML tags
    .replace(/javascript:/gi, '') // Remove javascript protocol
    .trim()
}

export const secureStorage = {
  setItem: (key: string, value: string) => {
    if (typeof window !== 'undefined') {
      // Use sessionStorage instead of localStorage for sensitive information
      sessionStorage.setItem(key, value)
    }
  },
  
  getItem: (key: string) => {
    if (typeof window !== 'undefined') {
      return sessionStorage.getItem(key)
    }
    return null
  },
  
  removeItem: (key: string) => {
    if (typeof window !== 'undefined') {
      sessionStorage.removeItem(key)
    }
  },
  
  clear: () => {
    if (typeof window !== 'undefined') {
      sessionStorage.clear()
    }
  }
}