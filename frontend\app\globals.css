@import 'tailwindcss';
@import 'tw-animate-css';

@custom-variant dark (&:is(.dark *));

:root {
  /* Enterprise Color Scheme - Light Mode */
  --background: oklch(0.99 0 0); /* Pure white background */
  --foreground: oklch(0.18 0 0); /* Deep charcoal gray #2C3E50 */
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.18 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.18 0 0);

  /* Primary: Deep Navy Blue #08244F */
  --primary: oklch(0.16 0.05 240);
  --primary-foreground: oklch(0.99 0 0);

  /* Secondary: Business Blue #0B5FFF */
  --secondary: oklch(0.55 0.15 250);
  --secondary-foreground: oklch(0.99 0 0);

  /* Neutral backgrounds */
  --muted: oklch(0.96 0 0); /* Light neutral #F4F6F9 */
  --muted-foreground: oklch(0.45 0 0); /* Medium gray #6C757D */
  --accent: oklch(0.96 0 0);
  --accent-foreground: oklch(0.18 0 0);

  /* Functional colors - low saturation */
  --destructive: oklch(0.55 0.12 25); /* Muted red */
  --destructive-foreground: oklch(0.99 0 0);

  /* Borders and inputs */
  --border: oklch(0.90 0 0); /* Light gray borders */
  --input: oklch(0.95 0 0);
  --ring: oklch(0.55 0.15 250); /* Business blue focus ring */

  /* Enterprise Chart Colors - Professional palette */
  --chart-1: oklch(0.16 0.05 240); /* Deep navy */
  --chart-2: oklch(0.55 0.15 250); /* Business blue */
  --chart-3: oklch(0.45 0 0); /* Medium gray */
  --chart-4: oklch(0.50 0.08 140); /* Muted success green */
  --chart-5: oklch(0.60 0.10 50); /* Muted warning orange */

  /* Reduced border radius for professional look */
  --radius: 0.25rem; /* 4px instead of 10px */

  /* Sidebar colors */
  --sidebar: oklch(0.98 0 0);
  --sidebar-foreground: oklch(0.18 0 0);
  --sidebar-primary: oklch(0.16 0.05 240);
  --sidebar-primary-foreground: oklch(0.99 0 0);
  --sidebar-accent: oklch(0.96 0 0);
  --sidebar-accent-foreground: oklch(0.18 0 0);
  --sidebar-border: oklch(0.90 0 0);
  --sidebar-ring: oklch(0.55 0.15 250);
}

.dark {
  /* Enterprise Color Scheme - Dark Mode */
  --background: oklch(0.12 0 0); /* Dark background */
  --foreground: oklch(0.95 0 0); /* Light text */
  --card: oklch(0.15 0 0);
  --card-foreground: oklch(0.95 0 0);
  --popover: oklch(0.15 0 0);
  --popover-foreground: oklch(0.95 0 0);

  /* Primary: Lighter navy for dark mode */
  --primary: oklch(0.65 0.12 250);
  --primary-foreground: oklch(0.12 0 0);

  /* Secondary: Muted business blue */
  --secondary: oklch(0.25 0 0);
  --secondary-foreground: oklch(0.95 0 0);

  /* Neutral backgrounds */
  --muted: oklch(0.20 0 0);
  --muted-foreground: oklch(0.70 0 0);
  --accent: oklch(0.20 0 0);
  --accent-foreground: oklch(0.95 0 0);

  /* Functional colors - muted for dark mode */
  --destructive: oklch(0.50 0.10 25);
  --destructive-foreground: oklch(0.95 0 0);

  /* Borders and inputs */
  --border: oklch(0.25 0 0);
  --input: oklch(0.20 0 0);
  --ring: oklch(0.65 0.12 250);

  /* Enterprise Chart Colors - Dark mode variants */
  --chart-1: oklch(0.65 0.12 250); /* Lighter business blue */
  --chart-2: oklch(0.55 0.10 200); /* Muted blue */
  --chart-3: oklch(0.60 0 0); /* Light gray */
  --chart-4: oklch(0.55 0.08 140); /* Muted green */
  --chart-5: oklch(0.65 0.08 50); /* Muted orange */

  /* Sidebar colors */
  --sidebar: oklch(0.15 0 0);
  --sidebar-foreground: oklch(0.95 0 0);
  --sidebar-primary: oklch(0.65 0.12 250);
  --sidebar-primary-foreground: oklch(0.12 0 0);
  --sidebar-accent: oklch(0.20 0 0);
  --sidebar-accent-foreground: oklch(0.95 0 0);
  --sidebar-border: oklch(0.25 0 0);
  --sidebar-ring: oklch(0.65 0.12 250);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
