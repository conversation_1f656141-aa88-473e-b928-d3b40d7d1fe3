export interface User {
  id: number
  phone: string
  user_name: string
  role: 'USER' | 'ADMIN'
  is_active: boolean
}

export interface LoginCredentials {
  phone: string
  password: string
}

export interface RegisterCredentials {
  phone: string
  password: string
}

export interface AuthResponse {
  code: number
  msg: string
  data: User
  tokens: {
    access: string
    refresh: string
  }
}

export interface ApiError {
  code: number
  msg: string
  data: null
}

/**
 * 后端统一的错误码枚举，与后端保持一致
 * 这些值用于前端错误消息映射
 */
export enum ErrorCode {
  OK = "ok",
  INVALID_CREDENTIALS = "invalid_credentials",
  INVALID_PHONE = "invalid_phone",
  INVALID_PASSWORD = "invalid_password",
  PHONE_EXISTS = "phone_exists",
  UNAUTHORIZED = "unauthorized",
  INVALID_TOKEN = "invalid_token",
  USER_NOT_FOUND = "user_not_found",
  INTERNAL_ERROR = "internal_error"
}

export interface AuthContextType {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  login: (credentials: LoginCredentials) => Promise<void>
  register: (credentials: RegisterCredentials) => Promise<void>
  logout: () => Promise<void>
  refreshUser: () => Promise<void>
}