from typing import Any
import time
import uuid
from fastapi import Response
from jose import jwt, JW<PERSON>rror
from jose.exceptions import ExpiredSignatureError
from passlib.context import CryptContext
from backend.app.shared.config import settings

ALGORITHM = "HS256"
COOKIE_ACCESS = "cookie_access"
COOKIE_REFRESH = "cookie_refresh"

# Password hash encryption
__pwd_ctx = CryptContext(schemes=["bcrypt"], deprecated="auto")

def hash_password(plain: str) -> str:
    """
    Password hash encryption
    """
    return __pwd_ctx.hash(plain)

def verify_password(plain: str, hashed: str) -> bool:
    """
    Password hash verification
    """
    return __pwd_ctx.verify(plain, hashed)

def _create_token(*, typ: str, user_id: int, expires_in_seconds: int) -> str:
    """
    Create JWT token
    """
    iat = int(time.time())
    exp = iat + int(expires_in_seconds)
    payload = {
        "sub": user_id,
        "typ": typ,
        "jti": str(uuid.uuid4()),
        "iat": iat,
        "exp": exp,
    }
    return jwt.encode(payload, settings.jwt_secret, algorithm=ALGORITHM)

def create_access_token(user_id: int) -> str:
    """
    Create access token
    """
    return _create_token(typ="access", user_id=user_id, expires_in_seconds=settings.access_expire_seconds)

def create_refresh_token(user_id: int) -> str:
    """
    Create refresh token
    """
    return _create_token(typ="refresh", user_id=user_id, expires_in_seconds=settings.refresh_expire_seconds)

def verify_token(token: str, expected_type: str) -> dict[str, Any]:
    """
    Verify token
    Args:
        token: JWT token string
        expected_type: Expected token type ('access' or 'refresh')
    Returns:
        dict: Token payload if valid
    Raises:
        ValueError: If token is invalid or expired
    """
    try:
        payload = jwt.decode(token, settings.jwt_secret, algorithms=[ALGORITHM])
    except ExpiredSignatureError:
        raise ValueError("token_expired")
    except JWTError :
        raise ValueError("invalid_token")

    if payload.get("typ") != expected_type:
        raise ValueError("invalid_token_type")

    return payload

def set_auth_cookies(response: Response, access_token: str, refresh_token: str) -> None:
    """
    Set authentication cookies in response
    Args:
        response: FastAPI response object
        access_token: JWT access token
        refresh_token: JWT refresh token
    """
    samesite = (settings.cookie_samesite or "Lax").lower()
    response.set_cookie(
        key=COOKIE_ACCESS,
        value=access_token,
        httponly=True,
        samesite=samesite,
        secure=bool(settings.cookie_secure),
        max_age=int(settings.access_expire_seconds),
        path="/",
    )
    response.set_cookie(
        key=COOKIE_REFRESH,
        value=refresh_token,
        httponly=True,
        samesite=samesite,
        secure=bool(settings.cookie_secure),
        max_age=int(settings.refresh_expire_seconds),
        path="/",
    )

def set_access_cookie(response: Response, access_token: str) -> None:
    """
    Set only the access token cookie (do not touch refresh cookie)
    Args:
        response: FastAPI response object
        access_token: JWT access token
    """
    samesite = (settings.cookie_samesite or "Lax").lower()
    response.set_cookie(
        key=COOKIE_ACCESS,
        value=access_token,
        httponly=True,
        samesite=samesite,
        secure=bool(settings.cookie_secure),
        max_age=int(settings.access_expire_seconds),
        path="/",
    )

def clear_auth_cookies(response: Response) -> None:
    """
    Clear authentication cookies from response
    Args:
        response: FastAPI response object
    """
    response.delete_cookie(COOKIE_ACCESS, path="/")
    response.delete_cookie(COOKIE_REFRESH, path="/")

