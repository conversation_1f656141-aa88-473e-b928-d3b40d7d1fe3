# Hermes (赫尔墨斯) 智能客服引擎系统

Hermes 是一个现代化的全栈应用，包含 React/Next.js 前端和 FastAPI 后端，旨在提供 AI 驱动的客服能力，包括用户认证、代理管理和分析仪表板。

## 项目结构

```
hermes/
├── backend/                 # FastAPI 后端
│   ├── app/
│   │   ├── main.py         # 应用入口
│   │   ├── shared/         # 共享层
│   │   │   ├── config.py   # 环境配置
│   │   │   ├── db.py       # 数据库初始化
│   │   │   └── ...         # 其他共享组件
│   │   └── modules/        # 业务模块
│   │       ├── auth/       # 认证模块
│   │       ├── user/       # 用户模块
│   │       └── points/     # 积分模块
│   ├── requirements.txt   # Python 依赖
│   └── .env               # 环境变量
├── frontend/               # Next.js 前端
├── migrations/             # 数据库迁移文件
└── docs/                  # 项目文档
```

## 快速开始

### 环境要求

- Python 3.10+
- Node.js 18+
- PostgreSQL 12+
- Redis (可选，用于缓存和限流)

### 后端设置

1. **安装依赖**
   ```bash
   cd backend
   pip install -r requirements.txt
   ```

2. **配置环境变量**
   ```bash
   cp .env.example .env
   # 编辑 .env 文件，配置数据库连接等参数
   ```

3. **数据库迁移**
   
   **首次初始化：**
   ```bash
   python -m aerich init-db
   ```
   
   **应用现有迁移：**
   ```bash
   python -m aerich upgrade
   ```
   
   **创建新迁移：**
   ```bash
   python -m aerich migrate --name "migration_name"
   python -m aerich upgrade
   ```

4. **启动后端服务**
   ```bash
   # 开发模式
   python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   
   # 生产模式
   python -m uvicorn app.main:app --host 0.0.0.0 --port 8000
   ```

### 前端设置

1. **安装依赖**
   ```bash
   cd frontend
   npm install
   ```

2. **启动开发服务器**
   ```bash
   npm run dev
   ```

3. **构建生产版本**
   ```bash
   npm run build
   npm start
   ```

## 数据库管理策略

本项目使用 **Aerich** 作为 Tortoise ORM 的迁移工具，统一管理数据库表结构。**不再在应用启动时自动创建表**。

### 迁移工作流程

1. **修改模型** - 在 `backend/app/modules/*/models.py` 中修改数据模型
2. **生成迁移** - 运行 `python -m aerich migrate --name "描述"`
3. **应用迁移** - 运行 `python -m aerich upgrade`
4. **验证** - 启动应用并测试功能

### 重要说明

- 应用启动时仅初始化数据库连接，不创建或修改表结构
- 所有表结构变更必须通过 Aerich 迁移完成
- 迁移文件存储在 `migrations/models/` 目录
- 开发环境和生产环境使用相同的迁移流程

## 环境变量配置

### 后端环境变量 (backend/.env)

```env
# 运行环境
HERMES_ENV=dev

# 数据库配置
PG_HOST=127.0.0.1
PG_PORT=5432
PG_USER=postgres
PG_PASSWORD=your_password
PG_DB=hermes

# JWT 配置
JWT_SECRET=change_me_in_production
ACCESS_EXPIRE_SECONDS=600
REFRESH_EXPIRE_SECONDS=1209600

# CORS 配置
CORS_ORIGINS=http://localhost:3000

# Redis 配置 (可选)
REDIS_URL=redis://127.0.0.1:6379/0
REDIS_PASSWORD=

# 限流配置
RATE_LIMIT_BACKEND=redis
RATE_LIMIT_PREFIX=rl

# OTP 配置
OTP_ENABLED=false
OTP_TTL_SECONDS=300

# Cookie 配置
COOKIE_SECURE=false
COOKIE_SAMESITE=Lax

# 令牌黑名单配置
TOKEN_BLOCKLIST_PREFIX=tb
```

## 开发指南

### 后端开发

- 使用 FastAPI + Tortoise ORM + PostgreSQL
- 遵循模块化架构，每个模块包含 models、schemas、routes、services
- 共享层提供可复用的基础能力
- 使用 Aerich 管理数据库迁移

### 前端开发

- 使用 Next.js 14+ App Router
- 采用 Tailwind CSS + Radix UI 组件
- 响应式设计，移动优先
- 使用 TypeScript 确保类型安全

### 代码规范

- 后端遵循 Python PEP 8 规范
- 前端使用 ESLint + Prettier
- 提交前运行代码检查和测试

## API 文档

后端 API 文档可通过以下方式访问：

- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 健康检查

后端提供健康检查端点：
```bash
curl http://localhost:8000/health
```

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查 PostgreSQL 服务是否运行
   - 验证 `.env` 文件中的数据库配置
   - 确保数据库已创建

2. **迁移失败**
   - 确保已正确配置 Aerich
   - 检查 `pyproject.toml` 中的 aerich 配置
   - 验证 `backend/app/shared/config.py` 中的 TORTOISE_ORM 配置

3. **应用启动失败**
   - 检查所有依赖是否已安装
   - 验证环境变量配置
   - 查看日志输出了解具体错误

## 许可证

MIT License