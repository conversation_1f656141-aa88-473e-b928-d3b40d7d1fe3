'use client'

import React from 'react'
import { useAuth as useAuthContext } from '@/contexts/auth-context'
import type { User } from '@/lib/types/auth'

export function useAuth() {
  const context = useAuthContext()
  
  // Auto-refresh user session periodically
  React.useEffect(() => {
    const refreshInterval = setInterval(() => {
      if (context.isAuthenticated) {
        context.refreshUser()
      }
    }, 15 * 60 * 1000) // Refresh every 15 minutes
    
    return () => clearInterval(refreshInterval)
  }, [context.isAuthenticated, context.refreshUser])
  
  // Handle page visibility changes
  React.useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && context.isAuthenticated) {
        context.refreshUser()
      }
    }
    
    document.addEventListener('visibilitychange', handleVisibilityChange)
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange)
  }, [context.isAuthenticated, context.refreshUser])
  
  return context
}

export function useAuthenticatedUser(): { user: User; isAuthenticated: true; isLoading: boolean } {
  const { user, isAuthenticated, isLoading } = useAuth()
  
  if (isLoading) {
    return { user: null as any, isAuthenticated: false, isLoading: true }
  }
  
  if (!isAuthenticated) {
    throw new Error('User must be authenticated')
  }
  
  return { user: user!, isAuthenticated: true, isLoading: false }
}