# 登录注册与积分系统技术规范

目标
- 实现邮箱密码登录注册与JWT会话
- 预留手机号OTP接口但默认关闭
- 提供角色与积分账本能力

架构与关键文件（按功能模块 + 共享层）
- 后端 FastAPI Tortoise PG Redis

- 目录蓝图
  - [backend/app/main.py](backend/app/main.py)
  - [backend/app/shared/](backend/app/shared/)
    - [config.py](backend/app/shared/config.py) 配置与环境变量加载
    - [db.py](backend/app/shared/db.py) 数据库初始化与连接管理（Tortoise）
    - [redis.py](backend/app/shared/redis.py) Redis 连接管理与缓存操作
    - [security.py](backend/app/shared/security.py) JWT 签发验证、密码哈希
    - [rate_limit.py](backend/app/shared/rate_limit.py) 通用限流组件（基于 Redis）
    - [response.py](backend/app/shared/response.py) 统一响应包装与异常处理
    - [utils.py](backend/app/shared/utils.py) 通用工具
  - [backend/app/modules/](backend/app/modules/)
    - [auth/](backend/app/modules/auth/)
      - [models.py](backend/app/modules/auth/models.py)
      - [schemas.py](backend/app/modules/auth/schemas.py)
      - [routes.py](backend/app/modules/auth/routes.py)
      - [services/](backend/app/modules/auth/services/)
        - [auth.py](backend/app/modules/auth/services/auth.py)
        - [sms.py](backend/app/modules/auth/services/sms.py) 预留 OTP
    - [user/](backend/app/modules/user/)
      - [models.py](backend/app/modules/user/models.py)
      - [schemas.py](backend/app/modules/user/schemas.py)
      - [routes.py](backend/app/modules/user/routes.py)
      - [services.py](backend/app/modules/user/services.py)
    - [points/](backend/app/modules/points/)
      - [models.py](backend/app/modules/points/models.py)
      - [schemas.py](backend/app/modules/points/schemas.py)
      - [routes.py](backend/app/modules/points/routes.py)
      - [services.py](backend/app/modules/points/services.py)

- 环境与依赖
  - [backend/.env.example](backend/.env.example)
  - [backend/pyproject.toml](backend/pyproject.toml)
  - [README.md](README.md)

- 装配约定
  - 在 [backend/app/main.py](backend/app/main.py) 中完成：
    - CORS 中间件、Cookie 策略与异常处理注册
    - include_router 注册模块路由：
      - modules.auth.routes.router 前缀 /auth
      - modules.user.routes.router 前缀 /users
      - modules.points.routes.router 前缀 /points
    - 启动时调用 [backend/app/shared/db.py](backend/app/shared/db.py) 的 Tortoise 初始化并加载 modules.*.models

- 跨模块依赖规则
  - 业务模块仅依赖 shared，不允许业务模块之间直接依赖
  - 业务模块不得直接访问其他模块的数据表
  - 共享层仅包含可复用的基础能力，不反向依赖业务模块

- 模块内分层约定
  - 每个业务模块目录包含：
    - models 定义 Tortoise ORM 模型 仅包含该模块的数据表
    - schemas 定义 Pydantic 请求与响应模型
    - services 承载业务逻辑 不直接依赖 FastAPI Request/Response
    - routes FastAPI 路由层 仅做参数校验 鉴权与调用 services
    - deps 依赖项与依赖注入 可选
  - 依赖方向
    - routes -> services -> shared
    - models 仅被 services 使用 不向外暴露
  - 事务与一致性
    - 涉及积分扣减与流水写入在同一事务内完成
  - 错误与响应
    - 统一使用 [backend/app/shared/response.py](backend/app/shared/response.py) 提供的包装

- 从原规划到模块化路径的迁移映射
  - [backend/app/db.py](backend/app/db.py) => [backend/app/shared/db.py](backend/app/shared/db.py)
  - [backend/app/models/user.py](backend/app/models/user.py) => [backend/app/modules/user/models.py](backend/app/modules/user/models.py)
  - [backend/app/models/points.py](backend/app/models/points.py) => [backend/app/modules/points/models.py](backend/app/modules/points/models.py)
  - [backend/app/schemas/user.py](backend/app/schemas/user.py) => [backend/app/modules/user/schemas.py](backend/app/modules/user/schemas.py)
  - [backend/app/schemas/auth.py](backend/app/schemas/auth.py) => [backend/app/modules/auth/schemas.py](backend/app/modules/auth/schemas.py)
  - [backend/app/core/security.py](backend/app/core/security.py) => [backend/app/shared/security.py](backend/app/shared/security.py)
  - [backend/app/core/rate_limit.py](backend/app/core/rate_limit.py) => [backend/app/shared/rate_limit.py](backend/app/shared/rate_limit.py)
  - [backend/app/services/sms.py](backend/app/services/sms.py) => [backend/app/modules/auth/services/sms.py](backend/app/modules/auth/services/sms.py)
  - [backend/app/services/points.py](backend/app/services/points.py) => [backend/app/modules/points/services.py](backend/app/modules/points/services.py)
  - [backend/app/routers/auth.py](backend/app/routers/auth.py) => [backend/app/modules/auth/routes.py](backend/app/modules/auth/routes.py)
  - [backend/app/routers/users.py](backend/app/routers/users.py) => [backend/app/modules/user/routes.py](backend/app/modules/user/routes.py)
  - [backend/app/routers/points.py](backend/app/routers/points.py) => [backend/app/modules/points/routes.py](backend/app/modules/points/routes.py)

- 模块依赖图
```mermaid
flowchart LR
auth[auth 模块] --> shared[shared 层]
user[user 模块] --> shared
points[points 模块] --> shared
```

会话策略
- JWT access 10m refresh 14d
- httpOnly Cookie SameSite Lax 开发期 Secure false
- Cookie 名称 cookie_access cookie_refresh
- 刷新轮换后续引入撤销存储

CORS 策略
- 允许 http://localhost:3000
- allow_credentials true
- 允许方法 GET POST OPTIONS

数据模型
users 表
- id 主键
- email 唯一非空
- phone 可空唯一 预留
- password_hash 非空
- role enum admin user 默认 user
- is_active bool 默认 true
- created_at updated_at

points_account 表
- user_id 唯一外键
- balance int 默认 0

points_tx 表
- id 主键
- user_id 外键
- delta int 不为 0
- reason 文本或枚举
- meta JSON 可空
- balance_after int
- created_at

API 列表
- POST /auth/register
- POST /auth/login
- GET /auth/me
- POST /auth/refresh
- POST /auth/logout
- POST /auth/otp/request 预留 501
- POST /auth/otp/verify 预留 501

请求响应示例
注册
Request:
{
  "email": "<EMAIL>",
  "password": "P@ssw0rd123"
}
Response 成功:
{
  "code": 0,
  "msg": "ok",
  "data": { "id": 1, "email": "<EMAIL>", "role": "user" }
}
失败:
{
  "code": 409,
  "msg": "email_exists"
}

登录
Request:
{
  "email": "<EMAIL>",
  "password": "P@ssw0rd123"
}
Response 成功:
{
  "code": 0,
  "msg": "ok",
  "data": { "id": 1, "email": "<EMAIL>", "role": "user" }
}
失败:
{
  "code": 401,
  "msg": "invalid_credentials"
}

/auth/me
Response:
{
  "code": 0,
  "msg": "ok",
  "data": { "id": 1, "email": "<EMAIL>", "role": "user" }
}
未登录:
{
  "code": 401,
  "msg": "unauthorized"
}

/auth/refresh
使用 cookie_refresh 无体
成功:
{
  "code": 0,
  "msg": "ok"
}
失败:
{
  "code": 401,
  "msg": "invalid_refresh"
}

/auth/logout
清空两枚 Cookie
成功:
{
  "code": 0,
  "msg": "ok"
}

错误码
- 0 ok
- 400 validation_error
- 401 unauthorized invalid_credentials invalid_refresh
- 403 forbidden
- 409 conflict email_exists
- 423 user_inactive
- 429 too_many_requests
- 500 internal_error
- 501 not_implemented

速率限制
- 登录 每用户每IP 5 分钟内最多 5 次
- 注册 每IP 每分钟 3 次
- 刷新 每用户 每分钟 30 次

密码策略
- 长度 8 到 64
- 建议包含大小写数字符号
- 使用 bcrypt 哈希

前端对接
- 新增 [frontend/lib/api.ts](frontend/lib/api.ts) 封装 fetch 基础：
  - 默认 credentials include
  - 统一请求头与错误包装 支持 401 自动引导登录
- 新增 [frontend/lib/auth.ts](frontend/lib/auth.ts) 提供认证方法：
  - login 使用邮箱与密码登录
  - register 注册账户
  - me 获取当前用户信息
  - refresh 刷新会话
  - logout 退出登录
- 修改 [frontend/app/page.tsx](frontend/app/page.tsx) 登录页改用 lib/auth.ts 并展示错误消息
- 修改 [frontend/app/dashboard/page.tsx](frontend/app/dashboard/page.tsx) 初次加载调用 /auth/me 未登录跳转

OTP 预留
- [backend/app/modules/auth/services/sms.py](backend/app/modules/auth/services/sms.py) 设计接口 send_code 校验与限流
- [backend/app/shared/rate_limit.py](backend/app/shared/rate_limit.py) 提供通用限流组件
- 开关由环境变量控制

环境变量样例
放置于 [backend/.env.example](backend/.env.example)
HERMES_ENV=dev
JWT_SECRET=change_me
ACCESS_EXPIRE_SECONDS=600
REFRESH_EXPIRE_SECONDS=1209600
PG_HOST=127.0.0.1
PG_PORT=5432
PG_USER=postgres
PG_PASSWORD=your_password
PG_DB=hermes
REDIS_URL=redis://127.0.0.1:6379/0
REDIS_PASSWORD=
RATE_LIMIT_BACKEND=redis
OTP_ENABLED=false
OTP_TTL_SECONDS=300
RATE_LIMIT_PREFIX=rl
TOKEN_BLOCKLIST_PREFIX=tb
CORS_ORIGINS=http://localhost:3000
COOKIE_SECURE=false
COOKIE_SAMESITE=Lax

环境变量说明
- HERMES_ENV 运行环境 dev test prod
- JWT_SECRET JWT 签名密钥 请使用强随机值并在生产中保密
- ACCESS_EXPIRE_SECONDS 访问令牌有效期秒数 默认 600
- REFRESH_EXPIRE_SECONDS 刷新令牌有效期秒数 默认 1209600
- PG_* PostgreSQL 数据库连接配置
- REDIS_URL Redis 连接 URL 格式 redis://[:password@]host[:port][/db_number]
- REDIS_PASSWORD Redis 密码（如果需要）
- RATE_LIMIT_BACKEND 限流后端 redis 或 memory
- OTP_ENABLED 是否启用 OTP 功能 默认 false
- OTP_TTL_SECONDS OTP 验证码有效期秒数 默认 300
- RATE_LIMIT_PREFIX 限流键前缀 默认 rl
- TOKEN_BLOCKLIST_PREFIX 令牌黑名单前缀 默认 tb
- CORS_ORIGINS 允许的前端来源 支持多个以逗号分隔
- COOKIE_SECURE 是否仅通过 HTTPS 发送 Cookie 生产必须为 true
- COOKIE_SAMESITE Cookie SameSite 策略 Lax Strict None

时序图
```mermaid
sequenceDiagram
participant FE as 前端
participant API as 后端
FE->>API: POST /auth/login
API-->>FE: Set-Cookie 两枚 并返回用户信息
FE->>API: GET /auth/me
API-->>FE: 用户信息
```

微服务演进建议
- 服务边界：auth user points 可拆分为独立服务 shared 下沉为公共包 hermes-shared
- 通信模式：同步 REST 请求 + 异步事件消息（如 Kafka 或 Redis Streams）实现跨服务最终一致
- 身份与令牌：由 auth 服务统一签发 校验库由 shared 提供 其他服务仅校验不签发
- 数据与事务：各服务独立数据库 避免分布式事务 通过事件驱动保持一致性
- API 网关：统一入口 负责认证与路由 限流与观测
- 可观测性：集中日志 追踪 与指标 贯穿 trace_id

路线图与里程碑
- 里程碑1 后端骨架与鉴权接口 可本地注册登录
- 里程碑2 刷新令牌撤销与限流 Redis 引入
  - 实现基于 Redis 的分布式限流
  - 实现刷新令牌撤销机制
  - 预留 OTP 验证码存储功能
- 里程碑3 积分账本与消费接口
- 里程碑4 OTP 短信上线
- 里程碑5 管理后台引入

开发提醒
- 所有响应使用统一包装
- 严格区分用户与管理员权限
- 事务中完成积分扣减并记录流水
- 生产部署时必须开启 Cookie Secure 并配置域名
- Redis 键空间使用前缀命名，避免冲突
- 生产环境 Redis 必须配置密码和网络安全策略
- 限流策略使用 Redis 实现，确保多实例一致性

登录流程图
```mermaid
flowchart TD
A[用户提交 email 密码] --> B[校验密码]
B -->|通过| C[签发 access refresh 写 httpOnly Cookie]
C --> D[将刷新令牌 jti 记录到 Redis]
D --> E[返回用户信息]
B -->|失败| F[返回 401]
```

Redis 键空间设计
- 限流键：{RATE_LIMIT_PREFIX}:{scope}:{key} 例如 rl:login:{user_id}:{ip}
- 刷新撤销：{TOKEN_BLOCKLIST_PREFIX}:{jti} -> 1（带过期等于刷新剩余寿命）
- OTP 验证码：otp:{scene}:{phone} -> code（TTL=OTP_TTL_SECONDS）
- 幂等键：idem:{biz}:{id} -> 1（短TTL）

变更影响
- 前端登录页与仪表盘将依赖后端接口与 Cookie
- 本地模拟登录逻辑删除

验收标准
- 能在本地完成注册登录 刷新 退出
- 未登录访问受限页面会重定向
- 新注册用户能正确创建积分账户 余额为 0

参考运行步骤将写入 [README.md](README.md)
- 前端登录页与仪表盘将依赖后端接口与 Cookie
- 本地模拟登录逻辑删除

验收标准
- 能在本地完成注册登录 刷新 退出
- 未登录访问受限页面会重定向
- 新注册用户能正确创建积分账户 余额为 0

参考运行步骤将写入 [README.md](README.md)