/**
 * 错误码与用户友好消息的映射
 * 
 * 这个文件定义了后端API返回的错误码与前端展示的用户友好消息之间的映射关系。
 * 后端使用统一的错误码枚举（如ok, invalid_credentials等），前端将其转换为可读性更好的错误消息。
 */

export type ErrorCode =
  | 'ok'
  | 'invalid_credentials'
  | 'invalid_phone'
  | 'invalid_password'
  | 'phone_exists'
  | 'unauthorized'
  | 'invalid_token'
  | 'user_not_found'
  | 'internal_error';

/**
 * 错误码与用户友好消息的映射对象
 */
export const ERROR_MESSAGES: Record<ErrorCode, string> = {
  ok: '操作成功',
  invalid_credentials: '账号或密码错误，请重试',
  invalid_phone: '手机号格式不正确',
  invalid_password: '密码格式不正确，密码长度应为8-64位',
  phone_exists: '该手机号已被注册',
  unauthorized: '请先登录后再操作',
  invalid_token: '登录已过期，请重新登录',
  user_not_found: '用户不存在',
  internal_error: '服务器内部错误，请稍后再试'
};

/**
 * 根据错误码获取用户友好的错误消息
 * 
 * @param code 错误码
 * @returns 用户友好的错误消息
 */
export function getErrorMessage(code: string | undefined): string {
  if (!code) return ERROR_MESSAGES.internal_error;
  
  return (code in ERROR_MESSAGES)
    ? ERROR_MESSAGES[code as ErrorCode]
    : `未知错误 (${code})`;
}