from typing import Optional
from tortoise import Tortoise, Model, fields
from tortoise.contrib.fastapi import register_tortoise
from backend.app.shared.config import settings

class TimestampModel(Model):
    """时间戳混入类，为模型添加created_at和updated_at字段"""
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    
# 处理数据库连接
async def init_db():
    await Tortoise.init(
        db_url=settings.database_url,
        modules={"models": ["backend.app.modules.auth.models"]},  # 修正
    )
    print("数据库连接成功")
    
async def close_db():
    await Tortoise.close_connections()
    print("数据库连接关闭")
    

def register_db(app):
    register_tortoise(
        app,
        db_url=settings.database_url,
        modules={"models": ["backend.app.modules.auth.models"]},  # 修正
        generate_schemas=False,
    )