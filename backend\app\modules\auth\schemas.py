from pydantic import BaseModel, Field, field_validator

class RegisterIn(BaseModel):
    phone: str = Field(max_length=20, description="手机号")
    # code: str = Field(max_length=6, description="验证码")
    password: str = Field(max_length=255, description="密码")
    
    @field_validator("phone")
    def check_phone(cls, v):
        if not v.isdigit():
            raise ValueError("手机号必须为数字")
        return v
    @field_validator("password")
    @classmethod
    def check_password(cls, v):
        if v.replace(" ", "") == "":
            raise ValueError("密码不能包含空格")
        return v

# 登录请求
class LoginIn(BaseModel):
    phone: str = Field(max_length=20, description="手机号")
    password: str = Field(max_length=255, description="密码")
    
    @field_validator("phone")
    def check_phone(cls, v):
        if not v.isdigit():
            raise ValueError("手机号必须为数字")
        return v
    @field_validator("password")
    @classmethod
    def check_password(cls, v):
        if v.replace(" ", "") == "":
            raise ValueError("密码不能包含空格")
        return v

# 用户信息
class UserOut(BaseModel):
    id: int = Field(description="用户ID")
    phone: str = Field(description="手机号")
    user_name: str = Field(description="用户名")
    role: str = Field(description="用户角色")
    is_active: bool = Field(description="是否启用")
